<?php
session_start();
include_once '../server/db_connect.php';

// Check if user is logged in as student
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $first_name = trim($_POST['first_name'] ?? '');
    $last_name = trim($_POST['last_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    
    // Validate inputs
    if (empty($first_name) || empty($last_name) || empty($email)) {
        $_SESSION['error'] = "Please fill out all required fields.";
        header("Location: ../../frontend/settings/student_settings.php");
        exit();
    }
    
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        // Update users table
        $stmt = $conn->prepare("UPDATE users SET first_name = ?, last_name = ?, email = ? WHERE id = ?");
        $stmt->bind_param("sssi", $first_name, $last_name, $email, $user_id);
        $stmt->execute();
        $stmt->close();
        
        // Update students table if phone is provided
        if (!empty($phone)) {
            $stmt = $conn->prepare("UPDATE students SET phone = ? WHERE user_id = ?");
            $stmt->bind_param("si", $phone, $user_id);
            $stmt->execute();
            $stmt->close();
        }
        
        // Commit transaction
        $conn->commit();
        
        $_SESSION['success'] = "Profile updated successfully!";
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        $_SESSION['error'] = "Error updating profile: " . $e->getMessage();
    }
    
    // Redirect back to settings page
    header("Location: ../../frontend/settings/student_settings.php");
    exit();
} else {
    // If not POST request, redirect to settings page
    header("Location: ../../frontend/settings/student_settings.php");
    exit();
}
?>