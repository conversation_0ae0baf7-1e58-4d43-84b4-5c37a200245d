<?php
// Start the session first
session_start();

// Check if user is logged in as parent
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'parent') {
    echo json_encode(['exists' => false, 'error' => 'Authentication required']);
    exit();
}

include_once '../server/db_connect.php';

if (isset($_POST['student_id'])) {
    $student_id = $_POST['student_id'];

    // Updated query to join with users table to get first_name, last_name, and grade
    $stmt = $conn->prepare("
        SELECT s.student_id, s.grade, u.first_name, u.last_name
        FROM students s
        JOIN users u ON s.user_id = u.id
        WHERE s.student_id = ?
    ");
    $stmt->bind_param("i", $student_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $student = $result->fetch_assoc();
        echo json_encode([
            'exists' => true,
            'name' => $student['first_name'] . ' ' . $student['last_name'],
            'grade' => $student['grade']
        ]);
    } else {
        echo json_encode(['exists' => false]);
    }
} else {
    echo json_encode(['exists' => false]);
}
?>
