<?php
session_start();
include '../../backend/server/db_connect.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get teacher_id
$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();
$teacher_id = $teacher['teacher_id'];
$stmt->close();

// Get all subjects
$stmt = $conn->prepare("SELECT subject_id, subject_name FROM subjects");
$stmt->execute();
$subjects_result = $stmt->get_result();
$subjects = [];
while ($row = $subjects_result->fetch_assoc()) {
    $subjects[] = $row;
}
$stmt->close();

// Get teacher's current subjects
$stmt = $conn->prepare("SELECT subject_id FROM teacher_subjects WHERE teacher_id = ?");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();
$current_subjects = [];
while ($row = $result->fetch_assoc()) {
    $current_subjects[] = $row['subject_id'];
}
$stmt->close();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_subject'])) {
    $new_subject_id = $_POST['subject_id'];

    // Check if already registered
    if (!in_array($new_subject_id, $current_subjects)) {
        $stmt = $conn->prepare("INSERT INTO teacher_subjects (teacher_id, subject_id) VALUES (?, ?)");
        $stmt->bind_param("ii", $teacher_id, $new_subject_id);
        $stmt->execute();
        $stmt->close();

        header("Location: add_subject.php?success=1");
        exit();
    } else {
        $error = "You are already registered to teach this subject.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Subject to Teach - Gurukula Institution</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8fafc;
            --dark-text: #2d3748;
            --light-text: #718096;
            --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Poppins', sans-serif;
            color: var(--dark-text);
        }

        .content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-weight: 700;
            color: var(--dark-text);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--light-text);
            font-weight: 400;
        }

        .card {
            border: none;
            border-radius: 16px;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
            overflow: hidden;
            height: 100%;
        }

        .card:hover {
            box-shadow: var(--hover-shadow);
            transform: translateY(-5px);
        }

        .card-header {
            border-bottom: none;
            padding: 1.5rem;
            font-weight: 600;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            position: relative;
            overflow: hidden;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 200%;
            background: rgba(255, 255, 255, 0.1);
            transform: rotate(30deg);
        }

        .card-header.success-header {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }

        .card-body {
            padding: 1.75rem;
        }

        .form-label {
            font-weight: 500;
            color: var(--dark-text);
            margin-bottom: 0.75rem;
        }

        .form-control, .form-select {
            border-radius: 10px;
            padding: 0.75rem 1rem;
            border: 1px solid #e2e8f0;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 1rem center;
            background-size: 16px 12px;
        }

        .btn {
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #64748b;
            border: none;
        }

        .btn-secondary:hover {
            background: #475569;
            transform: translateY(-2px);
        }

        .back-button {
            margin-bottom: 1.5rem;
        }

        .list-group {
            border-radius: 10px;
            overflow: hidden;
        }

        .list-group-item {
            padding: 1rem 1.25rem;
            border-left: none;
            border-right: none;
            transition: all 0.2s ease;
            background-color: transparent;
        }

        .list-group-item:hover {
            background-color: #f1f5f9;
            transform: translateX(5px);
        }

        .list-group-item:first-child {
            border-top: none;
        }

        .list-group-item:last-child {
            border-bottom: none;
        }

        .alert {
            border-radius: 10px;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
            border: none;
        }

        .alert-success {
            background-color: rgba(46, 204, 113, 0.15);
            color: #27ae60;
        }

        .alert-danger {
            background-color: rgba(231, 76, 60, 0.15);
            color: #c0392b;
        }

        .empty-state {
            padding: 3rem 1.5rem;
            text-align: center;
        }

        .empty-state-icon {
            font-size: 3.5rem;
            color: #cbd5e1;
            margin-bottom: 1.5rem;
        }

        .empty-state-text {
            color: var(--light-text);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .empty-state-subtext {
            color: #94a3b8;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .content {
                padding: 1.25rem;
            }

            .card-header {
                padding: 1.25rem;
            }

            .card-body {
                padding: 1.25rem;
            }
        }
    </style>
</head>
<body>
    <div class="content">
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="page-title">Subject Management</h1>
                    <p class="page-subtitle">Add and manage subjects you teach</p>
                </div>
                <div class="back-button">
                    <a href="../../frontend/dashboards/teacher_dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>

            <?php if (isset($_GET['success'])): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>Subject added successfully!
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i><?= $error ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="row g-4">
            <div class="col-lg-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-book me-2"></i>Add New Subject to Teach</h4>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-4">
                                <label for="subject_id" class="form-label">Select Subject</label>
                                <select name="subject_id" id="subject_id" class="form-select" required>
                                    <option value="">-- Select Subject --</option>
                                    <?php foreach ($subjects as $subject): ?>
                                        <?php if (!in_array($subject['subject_id'], $current_subjects)): ?>
                                            <option value="<?= $subject['subject_id'] ?>" <?= (isset($_GET['subject_id']) && $_GET['subject_id'] == $subject['subject_id']) ? 'selected' : '' ?>><?= $subject['subject_name'] ?></option>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text mt-2">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Select a subject you want to teach from the list
                                </div>
                            </div>
                            <div class="d-grid">
                                <button type="submit" name="add_subject" class="btn btn-primary">
                                    <i class="fas fa-plus-circle me-2"></i>Add Subject
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card h-100">
                    <div class="card-header success-header">
                        <h4 class="mb-0"><i class="fas fa-chalkboard-teacher me-2"></i>Subjects You Teach</h4>
                    </div>
                    <div class="card-body">
                        <?php if (empty($current_subjects)): ?>
                            <div class="empty-state">
                                <div class="empty-state-icon">
                                    <i class="fas fa-book-open"></i>
                                </div>
                                <h5 class="empty-state-text">No subjects registered yet</h5>
                                <p class="empty-state-subtext">Add your first subject from the form on the left</p>
                            </div>
                        <?php else: ?>
                            <div class="list-group">
                                <?php foreach ($subjects as $subject): ?>
                                    <?php if (in_array($subject['subject_id'], $current_subjects)): ?>
                                        <div class="list-group-item d-flex align-items-center">
                                            <div class="d-flex align-items-center">
                                                <div class="me-3 d-flex align-items-center justify-content-center"
                                                     style="width: 40px; height: 40px; background-color: rgba(46, 204, 113, 0.15); border-radius: 10px;">
                                                    <i class="fas fa-book text-success"></i>
                                                </div>
                                                <span class="fw-bold"><?= $subject['subject_name'] ?></span>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add animation to success message
            const successAlert = document.querySelector('.alert-success');
            if (successAlert) {
                successAlert.style.animation = 'fadeInDown 0.5s ease-out';

                // Auto-hide success message after 5 seconds
                setTimeout(() => {
                    successAlert.style.animation = 'fadeOutUp 0.5s ease-out forwards';
                }, 5000);
            }

            // Enhance form select with focus effect
            const formSelect = document.getElementById('subject_id');
            if (formSelect) {
                formSelect.addEventListener('change', function() {
                    if (this.value) {
                        this.classList.add('is-valid');
                    } else {
                        this.classList.remove('is-valid');
                    }
                });
            }

            // Add hover effect to list items
            const listItems = document.querySelectorAll('.list-group-item');
            listItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f1f5f9';
                    this.style.transform = 'translateX(5px)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'transparent';
                    this.style.transform = 'translateX(0)';
                });
            });

            // Add button click effect
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    this.classList.add('clicked');
                    setTimeout(() => {
                        this.classList.remove('clicked');
                    }, 200);
                });
            });
        });
    </script>

    <style>
        /* Animations */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeOutUp {
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(-20px);
                display: none;
            }
        }

        .btn.clicked {
            transform: scale(0.95);
        }

        .is-valid {
            border-color: var(--success-color) !important;
            background-color: rgba(46, 204, 113, 0.05) !important;
        }
    </style>
</body>
</html>
