<?php
// Fix the include path based on how the script is called
$base_path = __DIR__ . '/../../';
include_once $base_path . 'backend/server/db_connect.php';

// Check if email_logs table exists
$tableExists = false;
$result = $conn->query("SHOW TABLES LIKE 'email_logs'");
if ($result->num_rows > 0) {
    $tableExists = true;
    echo "Email logs table already exists.<br>";
}

// Create email_logs table if it doesn't exist
if (!$tableExists) {
    $sql = "CREATE TABLE email_logs (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        teacher_id INT(11) NOT NULL,
        subject VARCHAR(255) NOT NULL,
        sent_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    if ($conn->query($sql) === TRUE) {
        echo "Email logs table created successfully.<br>";
    } else {
        echo "Error creating email logs table: " . $conn->error . "<br>";
        exit;
    }
}

echo "Email logs setup complete.";
?>
