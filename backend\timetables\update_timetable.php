<?php
session_start();
include_once '../../backend/server/db_connect.php';

// Check if the user is logged in and is a teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get teacher_id
$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();
$teacher_id = $teacher['teacher_id'];
$stmt->close();

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $slot_id = $_POST['slot_id'];
    $subject_id = $_POST['subject_id'];
    $grade = $_POST['grade'];
    $day_of_week = $_POST['day_of_week'];
    $start_time = $_POST['start_time'];
    $end_time = $_POST['end_time'];

    // Validate times
    if (strtotime($end_time) <= strtotime($start_time)) {
        $_SESSION['error_message'] = "End time must be later than start time.";
        header("Location: ../../frontend/timetables/teacher_timetable.php?subject_id=$subject_id");
        exit();
    }

    // Verify that the slot belongs to this teacher
    $check_stmt = $conn->prepare("SELECT id FROM teacher_timetable WHERE id = ? AND teacher_id = ?");
    $check_stmt->bind_param("ii", $slot_id, $teacher_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();

    if ($check_result->num_rows === 0) {
        $_SESSION['error_message'] = "You don't have permission to edit this timetable entry.";
        header("Location: ../../frontend/timetables/teacher_timetable.php?subject_id=$subject_id");
        exit();
    }
    $check_stmt->close();

    // Update the timetable entry
    $update_stmt = $conn->prepare("UPDATE teacher_timetable SET subject_id = ?, grade = ?, day_of_week = ?, start_time = ?, end_time = ? WHERE id = ? AND teacher_id = ?");
    $update_stmt->bind_param("issssii", $subject_id, $grade, $day_of_week, $start_time, $end_time, $slot_id, $teacher_id);

    if ($update_stmt->execute()) {
        $_SESSION['success_message'] = "Timetable entry updated successfully.";
    } else {
        $_SESSION['error_message'] = "Error updating timetable entry: " . $update_stmt->error;
    }
    $update_stmt->close();

    header("Location: ../../frontend/timetables/teacher_timetable.php?subject_id=$subject_id");
    exit();
}
?>