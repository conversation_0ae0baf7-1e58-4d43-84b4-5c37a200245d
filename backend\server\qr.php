<?php
require_once '../../libs/phpqrcode/qrlib.php'; // path to the PHP QR library

// Student data

$qrData = "student_id=" . $student_id;

// Output path
$qrDir = "../../assets/qrcodes/";
if (!file_exists($qrDir)) {
    mkdir($qrDir, 0777, true);
}

$filename = $qrDir . "student_" . $student_id . ".png";

// Generate QR code
QRcode::png($qrData, $filename, QR_ECLEVEL_L, 5);

echo "<h3>QR Code Generated:</h3>";
echo "<img src='$filename' alt='QR Code'><br>";
echo "<a href='$filename' download>Download QR Code</a>";
?>
