<?php
include_once 'server/db_connect.php';

header('Content-Type: application/json');

try {
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Initialize data array
    $data = ["Paid" => 0, "Not Paid" => 0];
    
    // Query to count students by payment status
    $query = "SELECT 
                CASE 
                    WHEN payment_status = 'PAID' THEN 'Paid'
                    ELSE 'Not Paid'
                END as status,
                COUNT(*) as total 
              FROM students 
              GROUP BY payment_status";
    
    $result = $conn->query($query);
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $data[$row['status']] = (int)$row['total'];
        }
    } else {
        throw new Exception("Query failed: " . $conn->error);
    }
    
    // Log data for debugging
    error_log("Payment data: " . json_encode($data));
    
    echo json_encode($data);
    
} catch (Exception $e) {
    error_log("Error in fetch_financial_data.php: " . $e->getMessage());
    echo json_encode(['error' => $e->getMessage()]);
}
?>
