<?php
session_start();
include_once 'db_connect.php';

$message = '';
$message_type = '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $email = trim($_POST['email']);

    if (empty($email)) {
        $message = "Please enter your email address.";
        $message_type = "danger";
    } else {
        // Check if email exists in the database
        $stmt = $conn->prepare("SELECT id, first_name FROM users WHERE email = ?");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $stmt->store_result();

        if ($stmt->num_rows > 0) {
            $stmt->bind_result($user_id, $first_name);
            $stmt->fetch();

            // Check if there's an existing token for this user and delete it
            $delete_stmt = $conn->prepare("DELETE FROM password_reset_tokens WHERE user_id = ?");
            $delete_stmt->bind_param("i", $user_id);
            $delete_stmt->execute();
            $delete_stmt->close();

            // Generate a unique token
            $token = bin2hex(random_bytes(32));
            $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));

            // Store the token in the database
            $token_stmt = $conn->prepare("INSERT INTO password_reset_tokens (user_id, token, expires_at) VALUES (?, ?, ?)");
            $token_stmt->bind_param("iss", $user_id, $token, $expires);

            if ($token_stmt->execute()) {
                // Send email with reset link (in a real environment)
                $reset_link = "http://" . $_SERVER['HTTP_HOST'] . "/gurukula_lms/backend/server/reset_password.php?token=" . $token;

                // For demonstration purposes, we'll just show the link
                $message = "A password reset link has been sent to your email address. The link will expire in 1 hour.<br><br>
                            <strong>For demonstration:</strong> <a href='$reset_link'>$reset_link</a>";
                $message_type = "success";

                // In a production environment, you would send an actual email:
                /*
                $to = $email;
                $subject = "Password Reset - Gurukula LMS";
                $email_message = "Hello $first_name,\n\n";
                $email_message .= "You have requested to reset your password. Please click the link below to reset your password:\n\n";
                $email_message .= $reset_link . "\n\n";
                $email_message .= "This link will expire in 1 hour.\n\n";
                $email_message .= "If you did not request this, please ignore this email.\n\n";
                $email_message .= "Regards,\nGurukula LMS Team";
                $headers = "From: <EMAIL>";

                mail($to, $subject, $email_message, $headers);
                */
            } else {
                $message = "An error occurred. Please try again later.";
                $message_type = "danger";
            }

            $token_stmt->close();
        } else {
            // Don't reveal that the email doesn't exist for security reasons
            $message = "If your email address exists in our database, you will receive a password recovery link at your email address.";
            $message_type = "info";
        }

        $stmt->close();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - Gurukula LMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --light-bg: #f8f9fa;
            --dark-text: #2d3748;
            --light-text: #718096;
            --card-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }

        .forgot-password-container {
            width: 100%;
            max-width: 450px;
        }

        .card {
            border-radius: 20px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
        }

        .card-header::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 0;
            right: 0;
            margin: 0 auto;
            width: 40px;
            height: 40px;
            background: white;
            transform: rotate(45deg);
            z-index: 1;
        }

        .card-header h3 {
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .card-header p {
            opacity: 0.8;
            margin-bottom: 0;
        }

        .card-body {
            padding: 2.5rem 2rem 2rem;
            position: relative;
            z-index: 2;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-floating > .form-control {
            padding: 1.5rem 1rem;
            height: calc(3.5rem + 2px);
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .form-floating > .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
        }

        .form-floating > label {
            padding: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: 12px;
            padding: 1rem;
            font-size: 1rem;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px -5px rgba(67, 97, 238, 0.4);
        }

        .footer-links {
            text-align: center;
            margin-top: 1.5rem;
        }

        .footer-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            margin: 0 1rem;
            transition: all 0.3s ease;
        }

        .footer-links a:hover {
            color: rgba(255, 255, 255, 0.8);
        }

        .alert {
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="forgot-password-container">
        <div class="card">
            <div class="card-header">
                <h3>Forgot Password</h3>
                <p>Enter your email to reset your password</p>
            </div>
            <div class="card-body">
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?>">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="forgot_password.php">
                    <div class="form-floating mb-4">
                        <input type="email" name="email" id="email" class="form-control" placeholder="Email address" required>
                        <label for="email"><i class="fas fa-envelope me-2"></i>Email address</label>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>Send Reset Link
                    </button>
                </form>
            </div>
        </div>

        <div class="footer-links">
            <a href="../../frontend/login.php"><i class="fas fa-arrow-left me-1"></i>Back to Login</a>
        </div>
    </div>
</body>
</html>
