<?php
session_start();
include_once '../../backend/server/db_connect.php';
header('Content-Type: application/json');

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Log incoming data for debugging
$rawData = file_get_contents('php://input');
error_log("Received QR data: " . $rawData);

// Check if user is logged in and is a worker
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'worker') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Get JSON data
$data = json_decode($rawData, true);

if (!isset($data['qrData'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid QR data format']);
    exit();
}

// Parse QR data - handle both URL-style and JSON formats
$qrData = $data['qrData'];
$student_id = null;

// Try to parse as URL query string
if (strpos($qrData, 'student_id=') !== false) {
    parse_str($qrData, $qrParams);
    if (isset($qrParams['student_id'])) {
        $student_id = intval($qrParams['student_id']);
    }
}
// Try to parse as JSON
else if (substr($qrData, 0, 1) === '{') {
    $jsonData = json_decode($qrData, true);
    if (isset($jsonData['student_id'])) {
        $student_id = intval($jsonData['student_id']);
    }
}
// Try to extract just the number if it's only a number
else if (is_numeric($qrData)) {
    $student_id = intval($qrData);
}

if (!$student_id) {
    echo json_encode([
        'success' => false,
        'message' => 'Could not extract student ID from QR code',
        'received_data' => $qrData
    ]);
    exit();
}

$today = date('Y-m-d');

// Check if student exists and get additional information
$stmt = $conn->prepare("
    SELECT s.student_id, u.first_name, u.last_name, s.grade
    FROM students s
    JOIN users u ON s.user_id = u.id
    WHERE s.student_id = ?
");
$stmt->bind_param("i", $student_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Student not found',
        'student_id' => $student_id
    ]);
    exit();
}

$student = $result->fetch_assoc();
$student_name = $student['first_name'] . ' ' . $student['last_name'];
$grade = $student['grade'];

// Get the first subject for this student (or you could handle multiple subjects differently)
$subjectStmt = $conn->prepare("
    SELECT ss.subject_id, s.subject_name
    FROM student_subjects ss
    JOIN subjects s ON ss.subject_id = s.subject_id
    WHERE ss.student_id = ?
    LIMIT 1
");
$subjectStmt->bind_param("i", $student_id);
$subjectStmt->execute();
$subjectResult = $subjectStmt->get_result();

if ($subjectResult->num_rows > 0) {
    $subjectData = $subjectResult->fetch_assoc();
    $subject_id = $subjectData['subject_id'];
    $subject_name = $subjectData['subject_name'];
} else {
    $subject_id = null;
    $subject_name = "No Subject";
}
$subjectStmt->close();

// Calculate the week number based on the day of the month
$dayOfMonth = date('j');
$week_number = ceil($dayOfMonth / 7);
$week = 'Week ' . $week_number;

// Check if attendance already marked for today
$stmt = $conn->prepare("SELECT * FROM attendance WHERE student_id = ? AND date = ?");
$stmt->bind_param("is", $student_id, $today);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    // Already marked, update the time
    $stmt = $conn->prepare("
        UPDATE attendance
        SET time = NOW(),
            marked_by = ?,
            week = ?,
            student_name = ?,
            grade = ?,
            subject_id = ?,
            subject_name = ?
        WHERE student_id = ? AND date = ?
    ");
    $stmt->bind_param("isssisii", $_SESSION['user_id'], $week, $student_name, $grade, $subject_id, $subject_name, $student_id, $today);
    $stmt->execute();

    echo json_encode([
        'success' => true,
        'message' => 'Attendance updated',
        'student_id' => $student_id,
        'student_name' => $student_name,
        'grade' => $grade,
        'subject' => $subject_name,
        'week' => $week
    ]);
    exit();
}

// Mark new attendance
$stmt = $conn->prepare("
    INSERT INTO attendance
    (student_id, student_name, date, time, marked_by, week, grade, subject_id, subject_name)
    VALUES (?, ?, ?, NOW(), ?, ?, ?, ?, ?)
");
$stmt->bind_param("isssisis",
    $student_id,
    $student_name,
    $today,
    $_SESSION['user_id'],
    $week,
    $grade,
    $subject_id,
    $subject_name
);

if ($stmt->execute()) {
    echo json_encode([
        'success' => true,
        'message' => 'Attendance marked',
        'student_id' => $student_id,
        'student_name' => $student_name,
        'grade' => $grade,
        'subject' => $subject_name,
        'week' => $week
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $stmt->error,
        'student_id' => $student_id
    ]);
}

$stmt->close();
$conn->close();
?>





