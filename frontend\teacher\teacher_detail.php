<?php
include_once __DIR__ . '/../../backend/teacher/teacher_detail.php';

// Check for upload messages
$uploadSuccess = isset($_SESSION['upload_success']) ? $_SESSION['upload_success'] : false;
$uploadError = isset($_SESSION['upload_error']) ? $_SESSION['upload_error'] : '';

// Clear session messages after reading them
if (isset($_SESSION['upload_success'])) unset($_SESSION['upload_success']);
if (isset($_SESSION['upload_error'])) unset($_SESSION['upload_error']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Teacher Profile - Gurukula Institution</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

  <style>
    body {
      background-color: #f4f6f9;
      font-family: 'Inter', sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      padding: 20px;
    }
    .profile-card {
      width: 100%;
      max-width: 700px;
      background: #fff;
      padding: 35px;
      border-radius: 16px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease-in-out;
      margin: 0 auto;
    }
    .profile-card:hover {
      box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
    }
    .profile-card p {
      font-size: 1.1rem;
      margin-bottom: 18px;
    }
    .profile-card strong {
      width: 120px;
      display: inline-block;
      color: #02005F;
    }
    .profile-header {
      text-align: center;
      margin-bottom: 30px;
    }
    .profile-image-container {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;
    }
    .back-button {
      position: absolute;
      top: 20px;
      left: 20px;
      background-color: #02005F;
      color: white;
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .back-button:hover {
      background-color: #67BAFD;
      transform: scale(1.1);
    }
  </style>
</head>
<body>
<!-- Back button to return to dashboard -->
<a href="../dashboards/teacher_dashboard.php" class="back-button">
  <i class="fas fa-arrow-left"></i>
</a>

<div class="profile-card">
  <div class="profile-header">
    <h2 class="mb-4">Teacher Profile</h2>
  </div>

  <?php if ($uploadSuccess): ?>
  <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
    <strong>Success!</strong> Your profile image has been updated.
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
  </div>
  <?php endif; ?>

  <?php if ($uploadError): ?>
  <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
    <strong>Error!</strong> <?php echo htmlspecialchars($uploadError); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
  </div>
  <?php endif; ?>

  <!-- Profile Image Upload -->
  <div class="profile-image-container">
    <form action="../../backend/teacher/upload_image.php" method="POST" enctype="multipart/form-data" id="uploadForm">
      <label for="imageUpload">
        <img src="<?= htmlspecialchars('../../' . $imagePath) ?>" alt="Profile Picture" class="rounded-circle shadow mb-3" style="width: 150px; height: 150px; object-fit: cover; border: 3px solid #02005F; cursor: pointer;">
        <div class="text-center mb-3">
          <small class="text-muted"><i class="fas fa-camera me-1"></i>Click to change</small>
        </div>
      </label>
      <input type="file" name="profile_image" id="imageUpload" accept="image/*" style="display: none" onchange="document.getElementById('uploadForm').submit();">
      <input type="hidden" name="user_id" value="<?= $user_id ?>">
    </form>
  </div>

  <!-- Profile Details -->
  <div class="profile-details">
    <p><strong>Name:</strong> <?= htmlspecialchars($teacher['user_name']) ?></p>
    <p><strong>Email:</strong> <?= htmlspecialchars($teacher['email']) ?></p>
    <p><strong>Phone:</strong> <?= htmlspecialchars($teacher['phone']) ?></p>
    <p><strong>Subjects:</strong> <?= htmlspecialchars($teacher['subject_names'] ?: 'No subjects assigned') ?></p>
  </div>
</div>


<!-- Bootstrap Bundle JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
