<?php
// Start session at the very beginning before any output
session_start();

// Check if user is logged in and is an owner
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'owner') {
    header("Location: ../../backend/server/login.php");
    exit();
}

// Set current page for sidebar highlighting
$currentPage = basename($_SERVER['PHP_SELF']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Owner Dashboard - Gurukula Institution</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome for icons -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <!-- Google Fonts - Poppins -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <!-- Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    :root {
      --primary-color: #4e73df;
      --secondary-color: #6c757d;
      --success-color: #1cc88a;
      --info-color: #36b9cc;
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: #f8f9fc;
    }

    /* Main content styles */
    .content {
      margin-left: 280px;
      padding: 30px;
      transition: all 0.3s;
    }

    /* Dashboard header */
    .dashboard-header {
      margin-bottom: 24px;
      border-bottom: 1px solid #e3e6f0;
      padding-bottom: 20px;
    }

    .dashboard-header h2 {
      color: #5a5c69;
      font-weight: 700;
      margin-bottom: 8px;
    }

    /* Dashboard cards styles */
    .dashboard-card {
      background-color: #fff;
      border-radius: 0.75rem;
      border-left: 4px solid var(--primary-color);
      box-shadow: 0 0.15rem 1.75rem rgba(0, 0, 0, 0.15);
      padding: 1.5rem;
      position: relative;
      transition: transform 0.3s ease-in-out;
      margin-bottom: 24px;
      height: 100%;
    }

    .dashboard-card:hover {
      transform: translateY(-5px);
    }

    .dashboard-card i {
      font-size: 2rem;
      color: #dddfeb;
      position: absolute;
      right: 1rem;
      top: 1rem;
    }

    .dashboard-card h5 {
      font-size: 1.1rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      color: #4e73df;
    }

    .dashboard-card p {
      color: #858796;
      margin-bottom: 0;
    }

    .card-container {
      display: flex;
      gap: 24px;
      flex-wrap: wrap;
    }

    .card-container a {
      flex: 1 0 280px;
      max-width: 350px;
    }

    /* Stats card */
    .stats-card {
      background-color: #fff;
      border-radius: 0.75rem;
      box-shadow: 0 0.15rem 1.75rem rgba(0, 0, 0, 0.15);
      margin-bottom: 24px;
      overflow: hidden;
    }

    .stats-header {
      background-color: #f8f9fc;
      border-bottom: 1px solid #e3e6f0;
      padding: 1rem 1.25rem;
    }

    .stats-header h4 {
      margin: 0;
      color: #5a5c69;
      font-weight: 700;
      font-size: 1.1rem;
    }

    .stats-body {
      padding: 1.25rem;
    }

    .chart-container {
      position: relative;
      height: 250px;
      margin-bottom: 20px;
    }

    .key-metric {
      text-align: center;
      padding: 1.25rem;
      border-radius: 0.5rem;
      margin-bottom: 1rem;
      transition: all 0.3s;
    }

    .key-metric:hover {
      transform: translateY(-3px);
      box-shadow: 0 0.15rem 1.75rem rgba(0, 0, 0, 0.1);
    }

    .key-metric h3 {
      margin: 0;
      font-size: 2rem;
      font-weight: 700;
    }

    .key-metric p {
      margin: 5px 0 0;
      color: #858796;
      font-weight: 600;
    }

    .bg-light-success {
      background-color: rgba(40, 167, 69, 0.1);
      border-left: 4px solid #28a745;
    }

    .bg-light-danger {
      background-color: rgba(220, 53, 69, 0.1);
      border-left: 4px solid #dc3545;
    }

    .bg-light-primary {
      background-color: rgba(0, 123, 255, 0.1);
      border-left: 4px solid #007bff;
    }

    .text-success {
      color: #28a745;
    }

    .text-danger {
      color: #dc3545;
    }

    .text-primary {
      color: #007bff;
    }
  </style>
</head>
<body>

<?php
include_once __DIR__ . '/../../assets/owner_sidebar.php';
?>

  <!-- Main Content -->
  <div class="content">
    <div class="dashboard-header">
      <h2 class="text-body-secondary">Welcome back, <?php echo htmlspecialchars($_SESSION['user']['user_name'] ?? 'Owner'); ?>!<h2>
    </div>

    <!-- Financial Overview Section -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="stats-card">
          <div class="stats-header d-flex justify-content-between align-items-center">
            <h4><i class="fas fa-chart-line me-2"></i>Financial Overview</h4>
            <div class="dropdown">
              <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-calendar me-1"></i>This Month
              </button>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#">This Month</a></li>
                <li><a class="dropdown-item" href="#">Last Month</a></li>
                <li><a class="dropdown-item" href="#">This Year</a></li>
              </ul>
            </div>
          </div>
          <div class="stats-body">
            <div class="row">
              <div class="col-md-4">
                <div class="key-metric bg-light-success">
                  <h3 id="paidStudents">-</h3>
                  <p><i class="fas fa-user-check me-1"></i>Paid Students</p>
                </div>
              </div>
              <div class="col-md-4">
                <div class="key-metric bg-light-danger">
                  <h3 id="unpaidStudents">-</h3>
                  <p><i class="fas fa-user-clock me-1"></i>Unpaid Students</p>
                </div>
              </div>
              <div class="col-md-4">
                <div class="key-metric bg-light-primary">
                  <h3 id="totalRevenue">-</h3>
                  <p><i class="fas fa-coins me-1"></i>Total Revenue</p>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="chart-container">
                  <canvas id="paymentStatusChart"></canvas>
                </div>
              </div>
              <div class="col-md-6">
                <div class="chart-container">
                  <canvas id="monthlyRevenueChart"></canvas>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <h4 class="mb-3 text-body-secondary fw-bold"><i class="fas fa-tasks me-2"></i>Quick Actions</h4>
    <div class="card-container mt-3">
      <!-- Add Users Card -->
      <a href="../../backend/server/register.php" class="text-decoration-none">
        <div class="dashboard-card">
          <i class="fas fa-user-plus"></i>
          <h5>Add Users</h5>
          <p>Register new users to the system</p>
        </div>
      </a>
    </div>

<div class="card-container mt-3">
  <!-- Add Subjects Card -->
  <a href="../../backend/add_subject.php" class="text-decoration-none">
    <div class="dashboard-card">
      <i class="fas fa-book-medical"></i>
      <h5>Add Subjects</h5>
      <p>Create new subjects for classes</p>
    </div>
  </a>
</div>


  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script>
    // Load financial data
    async function loadFinancialData() {
      try {
        const response = await fetch('../../backend/fetch_financial_data.php');
        const data = await response.json();

        // Update metrics
        document.getElementById('paidStudents').textContent = data.Paid || 0;
        document.getElementById('unpaidStudents').textContent = data['Not Paid'] || 0;

        // Create payment status chart
        const statusCtx = document.getElementById('paymentStatusChart').getContext('2d');
        new Chart(statusCtx, {
          type: 'doughnut',
          data: {
            labels: ['Paid', 'Not Paid'],
            datasets: [{
              data: [data.Paid || 0, data['Not Paid'] || 0],
              backgroundColor: ['#28a745', '#dc3545'],
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'bottom'
              },
              title: {
                display: true,
                text: 'Payment Status Distribution'
              }
            }
          }
        });
      } catch (error) {
        console.error('Error loading payment data:', error);
      }
    }

    // Load revenue data
    async function loadRevenueData() {
      try {
        const response = await fetch('../../backend/fetch_revenue_data.php');
        const data = await response.json();

        // Update total revenue
        document.getElementById('totalRevenue').textContent = 'Rs. ' + data.totalRevenue.toLocaleString();

        // Create monthly revenue chart
        const revenueCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
        new Chart(revenueCtx, {
          type: 'bar',
          data: {
            labels: data.months,
            datasets: [{
              label: 'Monthly Revenue',
              data: data.values,
              backgroundColor: 'rgba(0, 123, 255, 0.5)',
              borderColor: 'rgba(0, 123, 255, 1)',
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              },
              title: {
                display: true,
                text: 'Monthly Revenue'
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  callback: function(value) {
                    return 'Rs. ' + value.toLocaleString();
                  }
                }
              }
            }
          }
        });
      } catch (error) {
        console.error('Error loading revenue data:', error);
      }
    }

    // Load data when page loads
    $(document).ready(function() {
      loadFinancialData();
      loadRevenueData();
    });
  </script>
</body>
</html>
