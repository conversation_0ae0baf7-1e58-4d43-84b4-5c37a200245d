<?php
/**
 * Central Authentication System
 *
 * This file provides functions to validate user access to pages based on roles.
 * Include this file at the beginning of any page that requires authentication.
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Define role-based access levels
define('ACCESS_PUBLIC', 0);     // Public pages (no login required)
define('ACCESS_STUDENT', 1);    // Student access
define('ACCESS_TEACHER', 2);    // Teacher access
define('ACCESS_PARENT', 3);     // Parent access
define('ACCESS_WORKER', 4);     // Worker access
define('ACCESS_OWNER', 5);      // Owner access

// Define role to access level mapping
$ROLE_ACCESS_LEVELS = [
    'student' => ACCESS_STUDENT,
    'teacher' => ACCESS_TEACHER,
    'parent' => ACCESS_PARENT,
    'worker' => ACCESS_WORKER,
    'owner' => ACCESS_OWNER
];

// Define page access requirements
$PAGE_ACCESS_REQUIREMENTS = [
    // Public pages
    'index.php' => ACCESS_PUBLIC,
    'frontend/login.php' => ACCESS_PUBLIC,
    'frontend/home.php' => ACCESS_PUBLIC,
    'backend/server/login.php' => ACCESS_PUBLIC,
    'backend/server/forgot_password.php' => ACCESS_PUBLIC,
    'backend/server/reset_password.php' => ACCESS_PUBLIC,

    // Student pages
    'frontend/dashboards/student_dashboard.php' => ACCESS_STUDENT,
    'frontend/student/student_marks.php' => ACCESS_STUDENT,
    'frontend/student/add_subject.php' => ACCESS_STUDENT,
    'frontend/student/add_parent.php' => ACCESS_STUDENT,
    'frontend/student/student_forum.php' => ACCESS_STUDENT,
    'frontend/student/assignments.php' => ACCESS_STUDENT,
    'frontend/settings/student_settings.php' => ACCESS_STUDENT,
    'frontend/timetables/student_timetable.php' => ACCESS_STUDENT,

    // Teacher pages
    'frontend/dashboards/teacher_dashboard.php' => ACCESS_TEACHER,
    'frontend/teacher/assignments.php' => ACCESS_TEACHER,
    'frontend/teacher/teacher_forum.php' => ACCESS_TEACHER,
    'frontend/teacher/manage_fees.php' => ACCESS_TEACHER,
    'frontend/teacher/subject_details.php' => ACCESS_TEACHER,
    'frontend/teacher/teacher_view_submissions.php' => ACCESS_TEACHER,
    'frontend/settings/teacher_settings.php' => ACCESS_TEACHER,
    'frontend/timetables/teacher_timetable.php' => ACCESS_TEACHER,

    // Parent pages
    'frontend/dashboards/parent_dashboard.php' => ACCESS_PARENT,
    'frontend/parent/student_performance.php' => ACCESS_PARENT,
    'frontend/payments/parent_payment.php' => ACCESS_PARENT,
    'frontend/payments/payment_success.php' => ACCESS_PARENT,
    'backend/payments/process_payment.php' => ACCESS_PARENT,
    'backend/payments/validate_student.php' => ACCESS_PARENT,
    'backend/payments/get_subject_fee.php' => ACCESS_PARENT,
    'frontend/settings/parent_settings.php' => ACCESS_PARENT,

    // Worker pages
    'frontend/dashboards/worker_dashboard.php' => ACCESS_WORKER,
    'frontend/worker/qr_scanner.php' => ACCESS_WORKER,
    'frontend/worker/monthly_attendance.php' => ACCESS_WORKER,
    'frontend/worker/monthly_payments.php' => ACCESS_WORKER,
    'frontend/settings/worker_settings.php' => ACCESS_WORKER,

    // Owner pages
    'frontend/dashboards/owner_dashboard.php' => ACCESS_OWNER,
    'frontend/timetables/owner_timetable.php' => ACCESS_OWNER,
    'frontend/financial_report.php' => ACCESS_OWNER,
    'frontend/settings/owner_settings.php' => ACCESS_OWNER
];

/**
 * Check if the current user has access to the current page
 *
 * @param string $page_path The path of the page to check access for
 * @return bool True if the user has access, false otherwise
 */
function hasPageAccess($page_path) {
    global $PAGE_ACCESS_REQUIREMENTS, $ROLE_ACCESS_LEVELS;

    // Remove base path from the page path
    $base_path = '/gurukula_lms/';
    if (strpos($page_path, $base_path) === 0) {
        $page_path = substr($page_path, strlen($base_path));
    }

    // If page is not in the requirements list, default to requiring owner access (most restrictive)
    $required_access = $PAGE_ACCESS_REQUIREMENTS[$page_path] ?? ACCESS_OWNER;

    // Public pages are accessible to everyone
    if ($required_access === ACCESS_PUBLIC) {
        return true;
    }

    // Check if user is logged in
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) {
        return false;
    }

    $user_role = $_SESSION['role'];
    $user_access_level = $ROLE_ACCESS_LEVELS[$user_role] ?? 0;

    // Special case: Owner has access to everything
    if ($user_role === 'owner') {
        return true;
    }

    // Check if user's access level matches the required access level
    return $user_access_level === $required_access;
}

/**
 * Enforce access control for the current page
 * Redirects to login page if access is denied
 *
 * @param string $page_path Optional: The path of the page to check access for (defaults to current page)
 * @return void
 */
function enforcePageAccess($page_path = null) {
    if ($page_path === null) {
        $page_path = $_SERVER['REQUEST_URI'];
    }

    if (!hasPageAccess($page_path)) {
        // User doesn't have access, redirect to login page
        header("Location: " . getLoginRedirectPath());
        exit();
    }
}

/**
 * Get the appropriate login redirect path based on the current page location
 *
 * @return string The path to redirect to
 */
function getLoginRedirectPath() {
    $current_path = $_SERVER['REQUEST_URI'];

    // Determine how many directories up we need to go to reach the login page
    $path_depth = substr_count($current_path, '/') - 1;
    if (strpos($current_path, '/gurukula_lms/') !== false) {
        $path_depth = substr_count($current_path, '/') - substr_count('/gurukula_lms/', '/');
    }

    $path_prefix = '';
    for ($i = 0; $i < $path_depth; $i++) {
        $path_prefix .= '../';
    }

    return $path_prefix . 'frontend/login.php?unauthorized=1';
}

/**
 * Add a new page to the access requirements list
 *
 * @param string $page_path The path of the page
 * @param int $access_level The required access level
 * @return void
 */
function addPageAccessRequirement($page_path, $access_level) {
    global $PAGE_ACCESS_REQUIREMENTS;
    $PAGE_ACCESS_REQUIREMENTS[$page_path] = $access_level;
}
?>
