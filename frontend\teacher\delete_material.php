<?php
session_start();
include '../../backend/server/db_connect.php';

// Check if user is logged in and is a teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../../backend/server/login.php");
    exit();
}

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header("Location: subject_details.php?error=invalid_id");
    exit();
}

$material_id = $_GET['id'];
$user_id = $_SESSION['user_id'];

// Get teacher_id
$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();
$teacher_id = $teacher['teacher_id'];
$stmt->close();

// Verify that the material belongs to this teacher
$stmt = $conn->prepare("SELECT file_path FROM materials WHERE id = ? AND teacher_id = ?");
$stmt->bind_param("ii", $material_id, $teacher_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    // Material doesn't exist or doesn't belong to this teacher
    header("Location: subject_details.php?error=unauthorized");
    exit();
}

// Get the file path to delete the actual file
$material = $result->fetch_assoc();
$file_path = '../../' . $material['file_path'];
$stmt->close();

// Delete the material from the database
$stmt = $conn->prepare("DELETE FROM materials WHERE id = ? AND teacher_id = ?");
$stmt->bind_param("ii", $material_id, $teacher_id);
$success = $stmt->execute();
$stmt->close();

// If database deletion was successful, try to delete the file
if ($success && file_exists($file_path)) {
    unlink($file_path); // Delete the file
}

// Redirect back to subject details with success message
header("Location: subject_details.php?success=material_deleted");
exit();
?>