<?php
session_start();
include_once '../../backend/server/db_connect.php';

// Check if user is logged in as student
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get student_id
$stmt = $conn->prepare("SELECT student_id FROM students WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();
$student_id = $student['student_id'];
$stmt->close();

// Check if parent already exists for this student
$stmt = $conn->prepare("SELECT p.parent_id, u.first_name, u.last_name, u.email
                        FROM parents p
                        JOIN users u ON p.user_id = u.id
                        WHERE p.child_student_id = ?");
$stmt->bind_param("i", $student_id);
$stmt->execute();
$result = $stmt->get_result();
$has_parent = $result->num_rows > 0;
$parent = $has_parent ? $result->fetch_assoc() : null;
$stmt->close();

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $password = trim($_POST['password']);
    $phone = trim($_POST['phone']);

    if (empty($name) || empty($email) || empty($password) || empty($phone)) {
        $error = "Please fill out all fields.";
    } else {
        // Check if email already exists
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $stmt->store_result();

        if ($stmt->num_rows > 0) {
            $error = "Email already exists. Please use a different email.";
        } else {
            $stmt->close();

            // Get parent role_id
            $role_stmt = $conn->prepare("SELECT role_id FROM roles WHERE role_name = 'parent'");
            $role_stmt->execute();
            $role_stmt->bind_result($role_id);
            $role_stmt->fetch();
            $role_stmt->close();

            if (!$role_id) {
                $error = "Parent role not found in the system.";
            } else {
                // Split the name into first and last name
                $name_parts = explode(' ', $name, 2);
                $first_name = $name_parts[0];
                $last_name = isset($name_parts[1]) ? $name_parts[1] : '';

                // Hash password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);

                // Begin transaction
                $conn->begin_transaction();

                try {
                    // Insert into users table
                    $stmt = $conn->prepare("INSERT INTO users (first_name, last_name, email, password, role_id) VALUES (?, ?, ?, ?, ?)");
                    $stmt->bind_param("ssssi", $first_name, $last_name, $email, $hashed_password, $role_id);
                    $stmt->execute();
                    $parent_user_id = $stmt->insert_id;
                    $stmt->close();

                    // Insert into parents table
                    $stmt = $conn->prepare("INSERT INTO parents (user_id, phone, child_student_id) VALUES (?, ?, ?)");
                    $stmt->bind_param("isi", $parent_user_id, $phone, $student_id);
                    $stmt->execute();
                    $stmt->close();

                    // Commit transaction
                    $conn->commit();

                    $success = "Parent registered successfully!";

                    // Refresh parent data
                    $stmt = $conn->prepare("SELECT p.parent_id, u.first_name, u.last_name, u.email
                                           FROM parents p
                                           JOIN users u ON p.user_id = u.id
                                           WHERE p.child_student_id = ?");
                    $stmt->bind_param("i", $student_id);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $has_parent = $result->num_rows > 0;
                    $parent = $has_parent ? $result->fetch_assoc() : null;
                    $stmt->close();

                } catch (Exception $e) {
                    // Rollback transaction on error
                    $conn->rollback();
                    $error = "Error registering parent: " . $e->getMessage();
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Parent - Gurukula Institution</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Poppins', sans-serif;
        }
        .content {
            padding: 40px;
            transition: all 0.3s;
            max-width: 100%;
        }
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            overflow: hidden;

        }
        .card-header {
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            color: white;
            border-radius: 20px 20px 0 0 !important;
            padding: 20px 25px;
        }
        .card-body {
            padding: 30px;
        }
        .form-control {
            border-radius: 10px;
            padding: 12px 15px;
            border: 1px solid #e1e5eb;
            background-color: #f9fafc;
            transition: all 0.3s;
        }
        .form-control:focus {
            box-shadow: 0 0 0 3px rgba(75, 108, 183, 0.15);
            border-color: #4b6cb7;
            background-color: #fff;
        }
        .form-label {
            font-weight: 500;
            margin-bottom: 8px;
            color: #495057;
        }
        .btn {
            padding: 12px 20px;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s;
        }
        .btn-primary {
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #3b5998 0%, #192f65 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 89, 152, 0.2);
        }
        .btn-secondary {
            background-color: #f8f9fa;
            border: 1px solid #dde2e6;
            color: #495057;
        }
        .btn-secondary:hover {
            background-color: #e9ecef;
            color: #212529;
            transform: translateY(-2px);
        }
        .alert {
            border-radius: 10px;
            padding: 15px 20px;
        }
        .alert-info {
            background-color: #e3f2fd;
            border-color: #b3e5fc;
            color: #0d47a1;
        }
        .parent-info {
            background-color: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #4b6cb7;
        }
        .parent-info h5 {
            color: #4b6cb7;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .parent-info p {
            margin-bottom: 12px;
            color: #495057;
        }
        .parent-info strong {
            font-weight: 600;
            color: #212529;
        }
        .input-group-text {
            background-color: #f8f9fa;
            border: 1px solid #e1e5eb;
            border-radius: 10px 0 0 10px;
        }
        @media (max-width: 768px) {
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>


    <div class="content">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6 col-md-8 col-sm-12 mx-auto">
                    <div class="d-flex justify-content-center align-items-center" style="min-height: 80vh;">
                        <div class="card w-100">
                            <div class="card-header">
                                <h4 class="mb-0 d-flex align-items-center">
                                    <i class="fas fa-user-plus me-3"></i>
                                    <?= $has_parent ? 'Parent Information' : 'Add Parent' ?>
                                </h4>
                            </div>
                            <div class="card-body">
                                <?php if (isset($error)): ?>
                                    <div class="alert alert-danger d-flex align-items-center" role="alert">
                                        <i class="fas fa-exclamation-circle me-2"></i>
                                        <div><?= $error ?></div>
                                    </div>
                                <?php endif; ?>

                                <?php if (isset($success)): ?>
                                    <div class="alert alert-success d-flex align-items-center" role="alert">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <div><?= $success ?></div>
                                    </div>
                                <?php endif; ?>

                                <?php if ($has_parent): ?>
                                    <div class="parent-info">
                                        <h5><i class="fas fa-user-check me-2"></i>Your parent is already registered</h5>
                                        <p><strong><i class="fas fa-user me-2"></i>Name:</strong> <?= htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']) ?></p>
                                        <p><strong><i class="fas fa-envelope me-2"></i>Email:</strong> <?= htmlspecialchars($parent['email']) ?></p>
                                        <p class="mb-0 text-muted"><i class="fas fa-info-circle me-2"></i>Your parent can login using their email and password.</p>
                                    </div>

                                    <div class="text-center mt-4">
                                        <a href="../dashboards/student_dashboard.php" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted mb-4">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Add your parent's information to connect their account with yours. This will allow them to monitor your academic progress.
                                    </p>

                                    <!-- Form to add parent -->
                                    <form method="POST" action="">
                                        <div class="mb-4">
                                            <label for="name" class="form-label">Full Name</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                                <input type="text" class="form-control" id="name" name="name" placeholder="Enter parent's full name" required>
                                            </div>
                                        </div>
                                        <div class="mb-4">
                                            <label for="email" class="form-label">Email Address</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                                <input type="email" class="form-control" id="email" name="email" placeholder="Enter parent's email address" required>
                                            </div>
                                        </div>
                                        <div class="mb-4">
                                            <label for="password" class="form-label">Password</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                                <input type="password" class="form-control" id="password" name="password" placeholder="Create a password for parent's account" required>
                                            </div>
                                            <div class="form-text text-muted">
                                                <i class="fas fa-shield-alt me-1"></i> Password should be at least 8 characters long
                                            </div>
                                        </div>
                                        <div class="mb-4">
                                            <label for="phone" class="form-label">Phone Number</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                                <input type="text" class="form-control" id="phone" name="phone" placeholder="Enter parent's phone number" required>
                                            </div>
                                        </div>
                                        <div class="d-grid gap-3">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-user-plus me-2"></i>Register Parent
                                            </button>
                                            <a href="../dashboards/student_dashboard.php" class="btn btn-secondary">
                                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                                            </a>
                                        </div>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add password visibility toggle
        document.addEventListener('DOMContentLoaded', function() {
            const passwordField = document.getElementById('password');
            if (passwordField) {
                const togglePassword = document.createElement('button');
                togglePassword.type = 'button';
                togglePassword.className = 'btn btn-outline-secondary';
                togglePassword.innerHTML = '<i class="fas fa-eye"></i>';
                togglePassword.style.borderRadius = '0 10px 10px 0';

                passwordField.parentNode.appendChild(togglePassword);

                togglePassword.addEventListener('click', function() {
                    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordField.setAttribute('type', type);
                    this.innerHTML = type === 'password' ? '<i class="fas fa-eye"></i>' : '<i class="fas fa-eye-slash"></i>';
                });
            }
        });
    </script>
</body>
</html>



