<?php
session_start();
include '../../backend/server/db_connect.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get student_id
$stmt = $conn->prepare("SELECT student_id FROM students WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();
$student_id = $student['student_id'];
$stmt->close();

// Get all subjects
$stmt = $conn->prepare("SELECT subject_id, subject_name FROM subjects");
$stmt->execute();
$subjects_result = $stmt->get_result();
$subjects = [];
while ($row = $subjects_result->fetch_assoc()) {
    $subjects[] = $row;
}
$stmt->close();

// Get current subjects
$stmt = $conn->prepare("SELECT subject_id FROM student_subjects WHERE student_id = ?");
$stmt->bind_param("i", $student_id);
$stmt->execute();
$result = $stmt->get_result();
$current_subjects = [];
while ($row = $result->fetch_assoc()) {
    $current_subjects[] = $row['subject_id'];
}
$stmt->close();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_subject'])) {
    $new_subject_id = $_POST['subject_id'];
    
    // Check if already registered
    if (!in_array($new_subject_id, $current_subjects)) {
        $stmt = $conn->prepare("INSERT INTO student_subjects (student_id, subject_id, payment_status) VALUES (?, ?, 'UNPAID')");
        $stmt->bind_param("ii", $student_id, $new_subject_id);
        $stmt->execute();
        $stmt->close();
        
        header("Location: add_subject.php?success=1");
        exit();
    } else {
        $error = "You are already registered for this subject.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Subject - Gurukula Institution</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --light-bg: #f8f9fa;
            --card-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        }
        
        body {
            background-color: var(--light-bg);
            font-family: 'Poppins', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            width: 100%;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--card-shadow);
            text-align: center;
        }
        
        .card {
            border-radius: 16px;
            box-shadow: var(--card-shadow);
            border: none;
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background-color: #fff;
            border-bottom: 3px solid var(--primary-color);
            padding: 1.25rem 1.5rem;
            font-weight: 600;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .form-select {
            border-radius: 10px;
            padding: 0.75rem 1rem;
            border: 1px solid #dee2e6;
            transition: all 0.3s;
        }
        
        .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-primary:hover {
            background-color: var(--secondary-color);
            transform: translateY(-3px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .btn-secondary {
            background-color: #6c757d;
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-secondary:hover {
            background-color: #5a6268;
            transform: translateY(-3px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .subject-card {
            border-radius: 12px;
            border-left: 5px solid var(--primary-color);
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: white;
            transition: all 0.3s;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
        }
        
        .subject-card:hover {
            transform: translateX(5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
        }
        
        .empty-state i {
            font-size: 4rem;
            color: #d1d5db;
            margin-bottom: 1rem;
        }
        
        .empty-state p {
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }
        
        .navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color);
        }
        
        footer {
            margin-top: auto;
            background-color: white;
            padding: 1rem 0;
            text-align: center;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="../dashboards/student_dashboard.php">
                <i class="fas fa-school me-2"></i>Gurukula Institution
            </a>
        
        </div>
    </nav>
    
    <div class="main-container">
        <div class="page-header">
            <h2 class="mb-0"><i class="fas fa-book-open me-2"></i>Subject Enrollment</h2>
            <p class="mb-0">Expand your knowledge by enrolling in new subjects</p>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-plus-circle me-2"></i>Add New Subject
                            </div>
                            <div class="card-body">
                                <?php if (isset($_GET['success'])): ?>
                                    <div class="alert alert-success d-flex align-items-center" role="alert">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <div>Subject added successfully! You can now access course materials.</div>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (isset($error)): ?>
                                    <div class="alert alert-danger d-flex align-items-center" role="alert">
                                        <i class="fas fa-exclamation-circle me-2"></i>
                                        <div><?= $error ?></div>
                                    </div>
                                <?php endif; ?>
                                
                                <form method="POST">
                                    <div class="mb-4">
                                        <label for="subject_id" class="form-label fw-bold">Select Subject</label>
                                        <select name="subject_id" id="subject_id" class="form-select form-select-lg" required>
                                            <option value="">-- Select Subject --</option>
                                            <?php 
                                            $available_subjects = false;
                                            foreach ($subjects as $subject): 
                                                if (!in_array($subject['subject_id'], $current_subjects)): 
                                                    $available_subjects = true;
                                            ?>
                                                <option value="<?= $subject['subject_id'] ?>"><?= $subject['subject_name'] ?></option>
                                            <?php 
                                                endif; 
                                            endforeach; 
                                            
                                            if (!$available_subjects): 
                                            ?>
                                                <option value="" disabled>No more subjects available for enrollment</option>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button type="submit" name="add_subject" class="btn btn-primary btn-lg" <?= !$available_subjects ? 'disabled' : '' ?>>
                                            <i class="fas fa-plus-circle me-2"></i>Enroll in Subject
                                        </button>
                                        <a href="../dashboards/student_dashboard.php" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-graduation-cap me-2"></i>Your Enrolled Subjects
                            </div>
                            <div class="card-body">
                                <?php if (empty($current_subjects)): ?>
                                    <div class="empty-state">
                                        <i class="fas fa-book"></i>
                                        <p>No subjects enrolled yet</p>
                                        <small>Start by adding your first subject from the form</small>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($subjects as $subject): ?>
                                        <?php if (in_array($subject['subject_id'], $current_subjects)): ?>
                                            <div class="subject-card d-flex align-items-center">
                                                <i class="fas fa-book text-primary me-3"></i>
                                                <div>
                                                    <h5 class="mb-0"><?= $subject['subject_name'] ?></h5>
                                                    <small class="text-muted">Enrolled</small>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <footer>
        <div class="container">
            <p class="mb-0">&copy; <?= date('Y') ?> Gurukula Institution. All rights reserved.</p>
        </div>
    </footer>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

