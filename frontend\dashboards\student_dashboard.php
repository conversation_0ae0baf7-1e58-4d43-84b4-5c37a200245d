<?php
// Include authentication check
require_once '../../backend/server/auth_check.php';

// Get student details here instead of including student_detail.php
include '../../backend/server/db_connect.php';

$user_id = $_SESSION['user_id'];

// Get student details
$stmt = $conn->prepare("
    SELECT u.first_name, u.last_name, u.email, s.grade, s.profile_image
    FROM users u
    JOIN students s ON u.id = s.user_id
    WHERE u.id = ?
");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$student = $stmt->get_result()->fetch_assoc();
$stmt->close();

// Combine first_name and last_name for display
$student_name = $student['first_name'] . ' ' . $student['last_name'];

// Image fallback
$imagePath = $student['profile_image'] ? '../../' . $student['profile_image'] : '../../assets/de.jpeg';

// Debug image path if needed
// echo "Image path: " . $imagePath;

// Check for upload messages
$uploadSuccess = isset($_SESSION['upload_success']) ? $_SESSION['upload_success'] : false;
$uploadError = isset($_SESSION['upload_error']) ? $_SESSION['upload_error'] : '';

// Clear session messages after reading them
if (isset($_SESSION['upload_success'])) unset($_SESSION['upload_success']);
if (isset($_SESSION['upload_error'])) unset($_SESSION['upload_error']);
?>
<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
  <meta charset="UTF-8">
  <title>Student Dashboard - Gurukula Institution</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome for icons -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <!-- Google Fonts - Poppins -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --light-bg: #f8f9fa;
      --card-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    }

    body {
      background-color: var(--light-bg);
      font-family: 'Poppins', sans-serif;
      font-size: 1rem;
    }

    /* Main content styles */
    .content {
      margin-left: 280px;
      padding: 30px;
      transition: all 0.3s ease;
    }

    /* Dashboard cards styles */
    .dashboard-card {
      background-color: #fff;
      border-radius: 16px;
      box-shadow: var(--card-shadow);
      text-align: center;
      padding: 1.5rem;
      transition: all 0.3s ease;
      height: 100%;
      border: none;
      position: relative;
      overflow: hidden;
    }

    .dashboard-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 5px;
      background: var(--primary-color);
    }

    .dashboard-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.15);
    }

    .dashboard-card i {
      font-size: 2.5rem;
      color: var(--primary-color);
      margin-bottom: 1rem;
      transition: all 0.3s ease;
    }

    .dashboard-card:hover i {
      transform: scale(1.1);
    }

    .dashboard-card h5 {
      font-size: 1.25rem;
      margin-bottom: 0.5rem;
      font-weight: 600;
      color: #333;
    }

    .dashboard-card p {
      color: #6c757d;
      font-size: 0.9rem;
    }

    .welcome-section {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--card-shadow);
    }

    .stats-card {
      background-color: white;
      border-radius: 16px;
      box-shadow: var(--card-shadow);
      padding: 1.5rem;
      margin-bottom: 2rem;
    }

    @media (max-width: 992px) {
      .content {
        margin-left: 0;
        padding: 20px;
      }
    }
  </style>
</head>
<body>

<?php include_once __DIR__ . '/../../assets/student_sidebar.php'; ?>

  <!-- Main Content -->
  <div class="content">
    <div class="welcome-section">
      <h2 class="mb-2">Welcome, <?php echo htmlspecialchars($student_name); ?>!</h2>
      <p class="mb-0">Access your courses, assignments, and manage your academic journey</p>
    </div>

    <?php if ($uploadSuccess): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
      <strong>Success!</strong> Your profile image has been updated.
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <?php if ($uploadError): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
      <strong>Error!</strong> <?php echo htmlspecialchars($uploadError); ?>
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <!-- Updated Student Profile Card -->
    <div class="profile-section mb-4">
      <div class="card border-0 shadow-sm rounded-4">
        <div class="card-body p-4">
          <div class="row align-items-center">
            <div class="col-md-3 text-center">
              <!-- Profile Image Upload -->
              <form action="../../backend/student/upload_student_image.php" method="POST" enctype="multipart/form-data" id="uploadForm">
                <label for="imageUpload" class="mb-0" style="cursor: pointer;">
                  <img src="<?= htmlspecialchars($imagePath) ?>" alt="Profile Picture" class="rounded-circle shadow"
                       style="width: 120px; height: 120px; object-fit: cover; border: 3px solid var(--primary-color); transition: all 0.3s ease;">
                  <div class="mt-2 text-center">
                    <small class="text-muted"><i class="fas fa-camera me-1"></i>Click to change</small>
                  </div>
                </label>
                <!-- Debug info - uncomment if needed -->
                <!-- <div class="mt-2 small text-muted">Image path: <?= htmlspecialchars($imagePath) ?></div> -->
                <input type="file" name="profile_image" id="imageUpload" accept="image/*" style="display: none" onchange="document.getElementById('uploadForm').submit();">
              </form>
            </div>
            <div class="col-md-9">
              <h3 class="fw-bold mb-3"><?= htmlspecialchars($student_name) ?></h3>
              <div class="d-flex align-items-center mb-2">
                <div class="bg-light rounded-pill p-2 me-3" style="width: 36px; height: 36px; display: flex; align-items: center; justify-content: center;">
                  <i class="fas fa-envelope text-primary"></i>
                </div>
                <p class="mb-0"><?= htmlspecialchars($student['email']) ?></p>
              </div>
              <div class="d-flex align-items-center">
                <div class="bg-light rounded-pill p-2 me-3" style="width: 36px; height: 36px; display: flex; align-items: center; justify-content: center;">
                  <i class="fas fa-graduation-cap text-primary"></i>
                </div>
                <p class="mb-0">Grade: <?= htmlspecialchars($student['grade']) ?></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <h4 class="mb-4">Quick Actions</h4>
    <div class="row g-4">
      <!-- Add parent Card -->
      <div class="col-md-6 col-lg-4">
        <a href="../../frontend/student/add_parent.php" class="text-decoration-none">
          <div class="dashboard-card">
            <i class="fa-solid fa-user-tie"></i>
            <h5>Add Parent</h5>
            <p>Connect your parent account for better communication</p>
          </div>
        </a>
      </div>

      <!-- Add Subject Card -->
      <div class="col-md-6 col-lg-4">
        <a href="../../frontend/student/add_subject.php" class="text-decoration-none">
          <div class="dashboard-card">
            <i class="fa-solid fa-book"></i>
            <h5>Add Subject</h5>
            <p>Enroll in new subjects and expand your learning</p>
          </div>
        </a>
      </div>

      <!-- Marks Card -->
      <div class="col-md-6 col-lg-4">
        <a href="../../frontend/student/student_marks.php" class="text-decoration-none">
          <div class="dashboard-card">
            <i class="fa-solid fa-chart-bar"></i>
            <h5>Marks</h5>
            <p>View your marks by subjects and assignments</p>
          </div>
        </a>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
