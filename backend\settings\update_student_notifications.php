<?php
session_start();
include_once '../server/db_connect.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header('Location: ../../frontend/login.php');
    exit;
}

$user_id = $_SESSION['user_id'];

// Get student_id from user_id
$stmt = $conn->prepare("SELECT student_id FROM students WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo "<script>alert('Student record not found.'); window.location.href='../../frontend/settings/student_settings.php';</script>";
    exit;
}

$student = $result->fetch_assoc();
$student_id = $student['student_id'];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Set default value to 0 if checkbox is not checked
    $popup_notifications = isset($_POST['popup_notifications']) ? 1 : 0;

    // Update the student's notification preferences
    $sql = "UPDATE students SET popup_notifications = ? WHERE student_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $popup_notifications, $student_id);

    if ($stmt->execute()) {
        echo "<script>alert('Notification preferences saved successfully.'); window.location.href='../../frontend/settings/student_settings.php';</script>";
    } else {
        echo "<script>alert('Failed to save preferences: " . $conn->error . "'); window.location.href='../../frontend/settings/student_settings.php';</script>";
    }
}

$conn->close();
?>