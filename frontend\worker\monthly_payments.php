<?php
// Include authentication check
require_once '../../backend/server/auth_check.php';

// Set current page for sidebar highlighting
$currentPage = basename($_SERVER['PHP_SELF']);
include_once '../../backend/server/db_connect.php';

// Get current month and year or use the ones from the query string
$month = isset($_GET['month']) ? intval($_GET['month']) : intval(date('m'));
$year = isset($_GET['year']) ? intval($_GET['year']) : intval(date('Y'));

// Get subject and grade filters
$subject_filter = isset($_GET['subject']) ? $_GET['subject'] : '';
$grade_filter = isset($_GET['grade']) ? $_GET['grade'] : '';

// Get subjects for dropdown
$subjects_query = "SELECT subject_id, subject_name FROM subjects ORDER BY subject_name";
$subjects_result = $conn->query($subjects_query);

// Get grades for dropdown
$grades_query = "SELECT DISTINCT grade FROM students ORDER BY grade";
$grades_result = $conn->query($grades_query);

// Build query to get students based on filters
$query = "SELECT s.student_id, CONCAT(u.first_name, ' ', u.last_name) AS student_name,
                 s.grade, s.payment_status,
                 s.last_payment_date, s.last_payment_period, s.next_payment_due,
                 sub.subject_id, sub.subject_name
          FROM students s
          JOIN users u ON s.user_id = u.id
          JOIN student_subjects ss ON s.student_id = ss.student_id
          JOIN subjects sub ON ss.subject_id = sub.subject_id
          WHERE 1=1";

if (!empty($subject_filter)) {
  $query .= " AND sub.subject_id = '$subject_filter'";
}

if (!empty($grade_filter)) {
  $query .= " AND s.grade = '$grade_filter'";
}

$query .= " ORDER BY s.grade, u.first_name, u.last_name";
$students_result = $conn->query($query);

// Format the month and year for display
$month_name = date('F', mktime(0, 0, 0, $month, 1, $year));
$period = sprintf('%04d-%02d', $year, $month);
$next_month = $month == 12 ? 1 : $month + 1;
$next_year = $month == 12 ? $year + 1 : $year;
$next_period = sprintf('%04d-%02d', $next_year, $next_month);
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Monthly Payments | Gurukula LMS</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Google Fonts - Poppins -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #1e3c72;
      --secondary-color: #2a5298;
      --accent-color: #67BAFD;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-color: #f8f9fa;
      --dark-color: #343a40;
      --border-radius: 15px;
      --card-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      --transition-speed: 0.3s;
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: #f4f7fe;
      margin: 0;
      padding: 0;
    }

    .content {
      margin-left: 280px;
      padding: 30px;
      transition: all var(--transition-speed) ease;
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
      padding-bottom: 15px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .page-title {
      font-size: 1.8rem;
      font-weight: 600;
      color: var(--primary-color);
      margin: 0;
      display: flex;
      align-items: center;
    }

    .page-title i {
      margin-right: 12px;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 10px;
      font-size: 1.2rem;
    }

    .info-card {
      background: white;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
      padding: 20px;
      margin-bottom: 25px;
      border-left: 5px solid var(--accent-color);
      display: flex;
      align-items: center;
    }

    .info-card i {
      font-size: 1.5rem;
      color: var(--accent-color);
      margin-right: 15px;
    }

    .month-nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
      flex-wrap: wrap;
      gap: 15px;
    }

    .month-selector {
      display: flex;
      align-items: center;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      padding: 8px 15px;
    }

    .month-display {
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--primary-color);
      margin: 0 15px;
      min-width: 150px;
      text-align: center;
    }

    .month-nav-btn {
      background: transparent;
      border: none;
      color: var(--primary-color);
      font-size: 1.1rem;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .month-nav-btn:hover {
      background: rgba(0, 0, 0, 0.05);
    }

    .filter-container {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      padding: 15px;
    }

    .filter-container .form-select {
      border-radius: 8px;
      border: 1px solid #e0e0e0;
      padding: 10px 15px;
      font-size: 0.95rem;
    }

    .filter-container .btn-primary {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border: none;
      border-radius: 8px;
      padding: 10px 20px;
      font-weight: 500;
    }

    .action-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .download-btn {
      background: linear-gradient(135deg, #2ecc71, #27ae60);
      border: none;
      border-radius: 10px;
      padding: 12px 25px;
      color: white;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 10px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(46, 204, 113, 0.2);
    }

    .download-btn:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 15px rgba(46, 204, 113, 0.3);
    }

    .payments-table {
      background: white;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
      overflow: hidden;
    }

    .payments-table .table {
      margin-bottom: 0;
    }

    .payments-table th {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      font-weight: 500;
      text-align: center;
      vertical-align: middle;
      padding: 15px 10px;
      border: none;
    }

    .payments-table td {
      text-align: center;
      vertical-align: middle;
      padding: 15px 10px;
      border-color: #f0f0f0;
    }

    .payments-table tbody tr:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }

    .paid {
      background-color: rgba(46, 204, 113, 0.1);
      color: var(--success-color);
      font-weight: 600;
      border-radius: 6px;
      padding: 5px 10px;
    }

    .unpaid {
      background-color: rgba(231, 76, 60, 0.1);
      color: var(--danger-color);
      font-weight: 600;
      border-radius: 6px;
      padding: 5px 10px;
    }

    .payment-actions {
      display: flex;
      gap: 8px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .btn-mark-paid {
      background-color: rgba(46, 204, 113, 0.1);
      color: var(--success-color);
      border: 1px solid rgba(46, 204, 113, 0.3);
      border-radius: 6px;
      padding: 6px 12px;
      font-size: 0.85rem;
      transition: all 0.2s ease;
    }

    .btn-mark-paid:hover {
      background-color: rgba(46, 204, 113, 0.2);
      border-color: var(--success-color);
    }

    .btn-mark-unpaid {
      background-color: rgba(231, 76, 60, 0.1);
      color: var(--danger-color);
      border: 1px solid rgba(231, 76, 60, 0.3);
      border-radius: 6px;
      padding: 6px 12px;
      font-size: 0.85rem;
      transition: all 0.2s ease;
    }

    .btn-mark-unpaid:hover {
      background-color: rgba(231, 76, 60, 0.2);
      border-color: var(--danger-color);
    }

    .btn-view-history {
      background-color: rgba(52, 152, 219, 0.1);
      color: #3498db;
      border: 1px solid rgba(52, 152, 219, 0.3);
      border-radius: 6px;
      padding: 6px 12px;
      font-size: 0.85rem;
      transition: all 0.2s ease;
    }

    .btn-view-history:hover {
      background-color: rgba(52, 152, 219, 0.2);
      border-color: #3498db;
    }

    .student-name {
      font-weight: 600;
      color: var(--primary-color);
    }

    .student-id {
      color: #6c757d;
      font-size: 0.9rem;
    }

    .badge-grade {
      background-color: var(--primary-color);
      color: white;
      padding: 5px 10px;
      border-radius: 6px;
      font-weight: 500;
      font-size: 0.85rem;
    }

    .badge-subject {
      background-color: var(--accent-color);
      color: white;
      padding: 5px 10px;
      border-radius: 6px;
      font-weight: 500;
      font-size: 0.85rem;
    }

    @media (max-width: 768px) {
      .content {
        margin-left: 0;
        padding: 15px;
      }

      .page-title {
        font-size: 1.5rem;
      }

      .month-nav {
        flex-direction: column;
        align-items: stretch;
      }

      .filter-container {
        flex-direction: column;
      }

      .payment-actions {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>

<?php include_once '../../assets/worker_sidebar.php'; ?>

<div class="content">
  <div class="page-header">
    <h1 class="page-title">
      <i class="fas fa-money-bill-wave"></i>
      <span>Monthly Payments</span>
    </h1>
  </div>

  <div class="info-card">
    <i class="fas fa-info-circle"></i>
    <div>
      Manage student payments for <strong><?= $month_name ?> <?= $year ?></strong>.

    </div>
  </div>

  <!-- Filter Controls -->
  <div class="month-nav">
    <div class="month-selector">
      <a href="?month=<?= $month == 1 ? 12 : $month - 1 ?>&year=<?= $month == 1 ? $year - 1 : $year ?>&subject=<?= $subject_filter ?>&grade=<?= $grade_filter ?>" class="month-nav-btn">
        <i class="fas fa-chevron-left"></i>
      </a>
      <div class="month-display"><?= $month_name ?> <?= $year ?></div>
      <a href="?month=<?= $next_month ?>&year=<?= $next_year ?>&subject=<?= $subject_filter ?>&grade=<?= $grade_filter ?>" class="month-nav-btn">
        <i class="fas fa-chevron-right"></i>
      </a>
    </div>

    <form method="GET" class="filter-container">
      <select name="subject" class="form-select">
        <option value="">All Subjects</option>
        <?php while ($subject = $subjects_result->fetch_assoc()): ?>
          <option value="<?= $subject['subject_id'] ?>" <?= $subject_filter == $subject['subject_id'] ? 'selected' : '' ?>>
            <?= $subject['subject_name'] ?>
          </option>
        <?php endwhile; ?>
      </select>

      <select name="grade" class="form-select">
        <option value="">All Grades</option>
        <?php while ($grade = $grades_result->fetch_assoc()): ?>
          <option value="<?= $grade['grade'] ?>" <?= $grade_filter == $grade['grade'] ? 'selected' : '' ?>>
            Grade <?= $grade['grade'] ?>
          </option>
        <?php endwhile; ?>
      </select>

      <input type="hidden" name="month" value="<?= $month ?>">
      <input type="hidden" name="year" value="<?= $year ?>">
      <button type="submit" class="btn btn-primary">
        <i class="fas fa-filter me-2"></i> Apply Filters
      </button>
    </form>
  </div>

  <!-- Action Bar -->
  <div class="action-bar">
    <div class="payment-summary">
      <?php if ($students_result->num_rows > 0): ?>
        <div class="badge bg-info p-2">
          <i class="fas fa-users me-1"></i> <?= $students_result->num_rows ?> Students
        </div>
      <?php endif; ?>
    </div>

    <button id="downloadReportBtn" class="download-btn">
      <i class="fas fa-download"></i>
      <span>Download Payment Report</span>
    </button>
  </div>

  <div class="payments-table">
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th style="width: 8%">ID</th>
            <th style="width: 18%" class="text-start">Student</th>
            <th style="width: 8%">Grade</th>
            <th style="width: 15%">Subject</th>
            <th style="width: 12%">Period</th>
            <th style="width: 10%">Status</th>
            <th style="width: 12%">Last Payment</th>
            <th style="width: 17%">Actions</th>
          </tr>
        </thead>
        <tbody>
          <?php if ($students_result->num_rows > 0): ?>
            <?php while ($student = $students_result->fetch_assoc()): ?>
              <?php
              // Check if payment is made for this period
              $is_paid = false;
              if ($student['last_payment_period'] == $period && $student['payment_status'] == 'PAID') {
                $is_paid = true;
              }
              $status_class = $is_paid ? 'paid' : 'unpaid';
              $status_text = $is_paid ? 'Paid' : 'Unpaid';
              ?>
              <tr>
                <td class="student-id"><?= $student['student_id'] ?></td>
                <td class="text-start student-name"><?= $student['student_name'] ?></td>
                <td><span class="badge-grade">Grade <?= $student['grade'] ?></span></td>
                <td><span class="badge-subject"><?= $student['subject_name'] ?></span></td>
                <td><?= $month_name ?> <?= $year ?></td>
                <td><span class="<?= $status_class ?>"><?= $status_text ?></span></td>
                <td>
                  <?= $student['last_payment_date'] ? date('d M Y', strtotime($student['last_payment_date'])) : 'Never' ?>
                </td>
                <td class="payment-actions">
                  <?php if (!$is_paid): ?>
                    <button class="btn-mark-paid mark-paid"
                            data-student-id="<?= $student['student_id'] ?>"
                            data-subject-id="<?= $student['subject_id'] ?>"
                            data-period="<?= $period ?>"
                            data-next-period="<?= $next_period ?>">
                      <i class="fas fa-check me-1"></i> Mark as Paid
                    </button>
                  <?php else: ?>
                    <button class="btn-mark-unpaid mark-unpaid"
                            data-student-id="<?= $student['student_id'] ?>"
                            data-subject-id="<?= $student['subject_id'] ?>"
                            data-period="<?= $period ?>">
                      <i class="fas fa-times me-1"></i> Mark as Unpaid
                    </button>
                  <?php endif; ?>
                  <button class="btn-view-history view-history"
                          data-student-id="<?= $student['student_id'] ?>"
                          data-subject-id="<?= $student['subject_id'] ?>">
                    <i class="fas fa-history me-1"></i> History
                  </button>
                </td>
              </tr>
            <?php endwhile; ?>
          <?php else: ?>
            <tr>
              <td colspan="8" class="text-center py-4">
                <div class="alert alert-info mb-0">
                  <i class="fas fa-info-circle me-2"></i>
                  No students found with the selected filters
                </div>
              </td>
            </tr>
          <?php endif; ?>
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Payment History Modal -->
<div class="modal fade" id="paymentHistoryModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content" style="border-radius: 15px; overflow: hidden;">
      <div class="modal-header" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); color: white; border: none;">
        <h5 class="modal-title d-flex align-items-center">
          <i class="fas fa-history me-2"></i> Payment History
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div id="paymentHistoryContent">
          <div class="text-center py-5">
            <div class="spinner-border" style="color: var(--accent-color); width: 3rem; height: 3rem;" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading payment history...</p>
          </div>
        </div>
      </div>
      <div class="modal-footer" style="border-top-color: rgba(0,0,0,0.05);">
        <button type="button" class="btn btn-secondary" style="border-radius: 8px; padding: 8px 20px;" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
$(document).ready(function() {
  // Handle marking a student as paid
  $('.mark-paid').click(function() {
    const studentId = $(this).data('student-id');
    const subjectId = $(this).data('subject-id');
    const period = $(this).data('period');
    const nextPeriod = $(this).data('next-period');
    const button = $(this);
    const row = button.closest('tr');

    // Confirm before marking as paid
    if (confirm('Are you sure you want to mark this payment as PAID?')) {
      // Send AJAX request to mark payment
      $.ajax({
        url: '../../backend/payments/manual_payment.php',
        type: 'POST',
        data: {
          student_id: studentId,
          subject_id: subjectId,
          payment_period: period,
          next_payment_due: nextPeriod,
          action: 'mark_paid'
        },
        success: function(response) {
          if (response.success) {
            // Update the UI
            row.find('td:nth-child(6)').removeClass('unpaid').addClass('paid').text('Paid');
            row.find('td:nth-child(7)').text(new Date().toLocaleDateString('en-GB', {
              day: '2-digit', month: 'short', year: 'numeric'
            }));

            // Replace the button with "Mark as Unpaid" button
            const unpaidBtn = `
              <button class="btn btn-sm btn-warning mark-unpaid"
                      data-student-id="${studentId}"
                      data-subject-id="${subjectId}"
                      data-period="${period}">
                <i class="fas fa-times me-1"></i> Mark as Unpaid
              </button>
            `;
            button.replaceWith(unpaidBtn);

            // Rebind event handler for the new button
            $('.mark-unpaid').last().click(function() {
              markUnpaid($(this));
            });

            alert('Payment marked as paid successfully!');
          } else {
            alert('Error: ' + response.message);
          }
        },
        error: function() {
          alert('An error occurred while updating payment status');
        }
      });
    }
  });

  // Function to handle marking a student as unpaid
  function markUnpaid(button) {
    const studentId = button.data('student-id');
    const subjectId = button.data('subject-id');
    const period = button.data('period');
    const row = button.closest('tr');

    // Confirm before marking as unpaid
    if (confirm('Are you sure you want to mark this payment as UNPAID?')) {
      // Send AJAX request to mark payment
      $.ajax({
        url: '../../backend/payments/manual_payment.php',
        type: 'POST',
        data: {
          student_id: studentId,
          subject_id: subjectId,
          payment_period: period,
          action: 'mark_unpaid'
        },
        success: function(response) {
          if (response.success) {
            // Update the UI
            row.find('td:nth-child(6)').removeClass('paid').addClass('unpaid').text('Unpaid');

            // Get next period for the paid button
            const currentPeriod = period.split('-');
            let nextMonth = parseInt(currentPeriod[1]) + 1;
            let nextYear = parseInt(currentPeriod[0]);

            if (nextMonth > 12) {
              nextMonth = 1;
              nextYear++;
            }

            const nextPeriod = `${nextYear}-${String(nextMonth).padStart(2, '0')}`;

            // Replace the button with "Mark as Paid" button
            const paidBtn = `
              <button class="btn btn-sm btn-success mark-paid"
                      data-student-id="${studentId}"
                      data-subject-id="${subjectId}"
                      data-period="${period}"
                      data-next-period="${nextPeriod}">
                <i class="fas fa-check me-1"></i> Mark as Paid
              </button>
            `;
            button.replaceWith(paidBtn);

            // Rebind event handler for the new button
            $('.mark-paid').last().click(function() {
              $('.mark-paid').click(function() {
                const studentId = $(this).data('student-id');
                const subjectId = $(this).data('subject-id');
                const period = $(this).data('period');
                const nextPeriod = $(this).data('next-period');
                const button = $(this);
                const row = button.closest('tr');

                // Rest of the mark-paid function...
              });
            });

            alert('Payment marked as unpaid successfully!');
          } else {
            alert('Error: ' + response.message);
          }
        },
        error: function() {
          alert('An error occurred while updating payment status');
        }
      });
    }
  }

  // Bind the markUnpaid function to existing buttons
  $('.mark-unpaid').click(function() {
    markUnpaid($(this));
  });

  // Handle viewing payment history
  $('.view-history').click(function() {
    const studentId = $(this).data('student-id');
    const subjectId = $(this).data('subject-id');

    // Show the modal
    $('#paymentHistoryModal').modal('show');

    // Load payment history
    $.ajax({
      url: '../../backend/payments/get_payment_history.php',
      type: 'GET',
      data: {
        student_id: studentId,
        subject_id: subjectId
      },
      success: function(response) {
        $('#paymentHistoryContent').html(response);
      },
      error: function() {
        $('#paymentHistoryContent').html('<div class="alert alert-danger">Failed to load payment history</div>');
      }
    });
  });

  // Handle download report button click
  $('#downloadReportBtn').click(function() {
    // Get current filters
    const month = <?= $month ?>;
    const year = <?= $year ?>;
    const subject = '<?= $subject_filter ?>';
    const grade = '<?= $grade_filter ?>';

    // Redirect to the PDF generation script with parameters
    window.location.href = `../../backend/payments/generate_payment_report.php?month=${month}&year=${year}&subject=${subject}&grade=${grade}`;
  });
});
</script>
</body>
</html>



