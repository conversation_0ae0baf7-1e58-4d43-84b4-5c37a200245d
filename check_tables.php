<?php
include 'backend/server/db_connect.php';

// Check assignments table structure
echo "ASSIGNMENTS TABLE STRUCTURE:\n";
$result = $conn->query('DESCRIBE assignments');
if ($result) {
    while($row = $result->fetch_assoc()) {
        echo $row['Field'] . " - " . $row['Type'] . "\n";
    }
} else {
    echo "Error: " . $conn->error . "\n";
}

// Check if there are any assignments
echo "\nASSIGNMENTS COUNT:\n";
$result = $conn->query('SELECT COUNT(*) as count FROM assignments');
if ($result) {
    $row = $result->fetch_assoc();
    echo "Total assignments: " . $row['count'] . "\n";
} else {
    echo "Error: " . $conn->error . "\n";
}

// Check subjects table structure
echo "\nSUBJECTS TABLE STRUCTURE:\n";
$result = $conn->query('DESCRIBE subjects');
if ($result) {
    while($row = $result->fetch_assoc()) {
        echo $row['Field'] . " - " . $row['Type'] . "\n";
    }
} else {
    echo "Error: " . $conn->error . "\n";
}

// Check teacher_subjects table structure
echo "\nTEACHER_SUBJECTS TABLE STRUCTURE:\n";
$result = $conn->query('DESCRIBE teacher_subjects');
if ($result) {
    while($row = $result->fetch_assoc()) {
        echo $row['Field'] . " - " . $row['Type'] . "\n";
    }
} else {
    echo "Error: " . $conn->error . "\n";
}

// Close connection
$conn->close();
?>
