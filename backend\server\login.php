<?php
session_start();
include_once 'db_connect.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (!isset($_POST['email']) || !isset($_POST['password'])) {
        $_SESSION['login_error'] = "Missing email or password.";
        header("Location: /gurukula_lms/frontend/login.php");
        exit();
    }

    $email = trim($_POST['email']);
    $password = trim($_POST['password']);

    // SQL query to get user details
    $stmt = $conn->prepare("
        SELECT u.id, u.first_name, u.last_name, u.password, r.role_name
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        WHERE u.email = ?
    ");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $stmt->store_result();

    if ($stmt->num_rows > 0) {
        $stmt->bind_result($id, $first_name, $last_name, $hashed_password, $role);
        $stmt->fetch();

        if (password_verify($password, $hashed_password)) {
            // Set session variables
            $_SESSION['user_id'] = $id;
            $_SESSION['role'] = $role;
            $_SESSION['name'] = $first_name . ' ' . $last_name;
            $_SESSION['login_time'] = time();

            // Redirect based on role
            switch ($role) {
                case 'owner':
                    header("Location: /gurukula_lms/frontend/dashboards/owner_dashboard.php");
                    break;
                case 'student':
                    header("Location: /gurukula_lms/frontend/dashboards/student_dashboard.php");
                    break;
                case 'teacher':
                    header("Location: /gurukula_lms/frontend/dashboards/teacher_dashboard.php");
                    break;
                case 'worker':
                    header("Location: /gurukula_lms/frontend/dashboards/worker_dashboard.php");
                    break;
                case 'parent':
                    header("Location: /gurukula_lms/frontend/dashboards/parent_dashboard.php");
                    break;
                default:
                    $_SESSION['login_error'] = "Unknown role.";
                    header("Location: /gurukula_lms/frontend/login.php");
            }
            exit();
        } else {
            $_SESSION['login_error'] = "Invalid email or password.";
            header("Location: /gurukula_lms/frontend/login.php");
            exit();
        }
    } else {
        $_SESSION['login_error'] = "User not found.";
        header("Location: /gurukula_lms/frontend/login.php");
        exit();
    }
    $stmt->close();
}
$conn->close();

// If someone tries to access this page directly, redirect to login
if ($_SERVER["REQUEST_METHOD"] != "POST") {
    header("Location: /gurukula_lms/frontend/login.php");
    exit();
}
?>
