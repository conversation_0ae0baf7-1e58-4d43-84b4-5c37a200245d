<?php
// Include the database connection
include_once 'server/db_connect.php';

// Set headers
header('Content-Type: application/json');

try {
    // Check if connection is established
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Get total revenue
    $total_query = "SELECT SUM(amount) as total FROM payment_requests WHERE status = 'PAID'";
    $total_result = $conn->query($total_query);
    $total_row = $total_result->fetch_assoc();
    $total_revenue = $total_row['total'] ?? 0;
    
    // Get monthly revenue for the last 6 months
    $months_query = "
        SELECT 
            DATE_FORMAT(payment_date, '%b %Y') as month,
            SUM(amount) as revenue
        FROM 
            payment_requests
        WHERE 
            status = 'PAID' 
            AND payment_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY 
            DATE_FORMAT(payment_date, '%Y-%m')
        ORDER BY 
            payment_date ASC
    ";
    
    $months_result = $conn->query($months_query);
    
    $months = [];
    $values = [];
    
    if ($months_result && $months_result->num_rows > 0) {
        while ($row = $months_result->fetch_assoc()) {
            $months[] = $row['month'];
            $values[] = (float)$row['revenue'];
        }
    } else {
        // If no data, provide sample data for demonstration
        $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
        $values = [0, 0, 0, 0, 0, 0];
    }
    
    // Return data
    echo json_encode([
        'totalRevenue' => (float)$total_revenue,
        'months' => $months,
        'values' => $values
    ]);
    
} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()]);
}

// Don't close the connection here as it might be used elsewhere
// The connection will be closed when the script ends
?>
