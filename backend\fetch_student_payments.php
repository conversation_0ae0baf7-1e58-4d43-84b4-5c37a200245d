<?php
include_once 'server/db_connect.php';

header('Content-Type: application/json');

try {
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Get filter parameters
    $subject = isset($_GET['subject']) ? $_GET['subject'] : '';
    $grade = isset($_GET['grade']) ? $_GET['grade'] : '';
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    
    // Build query with filters
    $query = "SELECT 
                s.student_id,
                CONCAT(u.first_name, ' ', u.last_name) as name,
                s.grade,
                sub.subject_name,
                pr.amount,
                CASE 
                    WHEN s.payment_status = 'PAID' THEN 'Paid'
                    ELSE 'Not Paid'
                END as status,
                s.last_payment_date as payment_date
              FROM 
                students s
              JOIN 
                users u ON s.user_id = u.id
              LEFT JOIN 
                student_subjects ss ON s.student_id = ss.student_id
              LEFT JOIN 
                subjects sub ON ss.subject_id = sub.subject_id
              LEFT JOIN 
                payment_requests pr ON s.student_id = pr.student_id AND pr.status = 'PAID'";
    
    // Add filters
    $conditions = [];
    $params = [];
    $types = '';
    
    if (!empty($subject)) {
        $conditions[] = "sub.subject_id = ?";
        $params[] = $subject;
        $types .= 'i';
    }
    
    if (!empty($grade)) {
        $conditions[] = "s.grade = ?";
        $params[] = $grade;
        $types .= 's';
    }
    
    if (!empty($status)) {
        if ($status == 'Paid') {
            $conditions[] = "s.payment_status = 'PAID'";
        } else {
            $conditions[] = "s.payment_status != 'PAID'";
        }
    }
    
    if (!empty($conditions)) {
        $query .= " WHERE " . implode(' AND ', $conditions);
    }
    
    $query .= " GROUP BY s.student_id, sub.subject_id";
    
    // Prepare and execute statement
    $stmt = $conn->prepare($query);
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $students = [];
    while ($row = $result->fetch_assoc()) {
        $students[] = $row;
    }
    
    echo json_encode($students);
    
} catch (Exception $e) {
    error_log("Error in fetch_student_payments.php: " . $e->getMessage());
    echo json_encode(['error' => $e->getMessage()]);
}
?>