<?php $currentPage = basename($_SERVER['PHP_SELF']); ?>

<!-- Mobile menu toggle button -->
<button class="mobile-menu-toggle d-md-none">
  <i class="fas fa-bars"></i>
</button>

<div class="sidebar">
    <h3><i class="fa-solid fa-bars"></i> <span>Gurukula</span></h3>
    <a href="../dashboards/student_dashboard.php" class="<?= $currentPage === 'student_dashboard.php' ? 'active' : '' ?>">
      <i class="fas fa-home"></i> <span>Dashboard</span>
    </a>
    <a href="../student/my_courses.php" class="<?= $currentPage === 'my_courses.php' || $currentPage === 'course_materials.php' ? 'active' : '' ?>">
      <i class="fa-solid fa-book-open"></i> <span>My courses</span>
    </a>
    <a href="../student/student_assignments.php" class="<?= $currentPage === 'student_assignments.php' ? 'active' : '' ?>">
      <i class="fa-solid fa-book"></i> <span>Assignments</span>
    </a>
    <a href="../forum/student_forum.php" class="<?= $currentPage === 'student_forum.php' ? 'active' : '' ?>">
      <i class="fas fa-comments"></i> <span>Forum</span>
    </a>
    <a href="../timetables/student_timetables.php" class="<?= $currentPage === 'student_timetables.php' ? 'active' : '' ?>">
      <i class="fa-solid fa-calendar-minus"></i> <span>Time Table</span>
    </a>
    <a href="../settings/student_settings.php" class="<?= $currentPage === 'student_settings.php' ? 'active' : '' ?>">
      <i class="fa-solid fa-gear"></i> <span>Settings</span>
    </a>
    <a href="../home.php" class="text-danger">
      <i class="fas fa-sign-out-alt"></i> <span>Logout</span>
    </a>
</div>

<!-- Include responsive CSS and JS -->
<link rel="stylesheet" href="../../assets/css/responsive.css">
<script src="../../assets/js/responsive-nav.js" defer></script>

<style>
/* Sidebar styles */
  .sidebar {
    height: 100vh;
    width: 260px;
    position: fixed;
    top: 0;
    left: 0;
    background-color: #02005F;
    padding: 25px;
    color: #fff;
    transition: all 0.3s;
    z-index: 1040;
  }
  .sidebar h3 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 1.8rem;
  }
  .sidebar a {
    color: #fff;
    text-decoration: none;
    display: block;
    padding: 12px;
    margin-bottom: 12px;
    border-radius: 8px;
    font-size: 1.2rem;
    transition: 0.3s;
  }
  .sidebar a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
  }
  .sidebar a:hover,
  .sidebar a.active {
    background-color: #67BAFD;
    transform: scale(1.05);
  }
</style>