<?php
session_start();
include '../../backend/server/db_connect.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get student's grade
$stmt = $conn->prepare("SELECT s.student_id, s.grade FROM students s
                        WHERE s.user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();
$stmt->close();

$grade = $student['grade'];
$student_id = $student['student_id'];

// Get student's subjects
$stmt = $conn->prepare("SELECT ss.subject_id, s.subject_name
                       FROM student_subjects ss
                       JOIN subjects s ON ss.subject_id = s.subject_id
                       WHERE ss.student_id = ?");
$stmt->bind_param("i", $student_id);
$stmt->execute();
$subjects_result = $stmt->get_result();
$subjects = [];

while ($subject = $subjects_result->fetch_assoc()) {
    $subjects[] = [
        'subject_id' => $subject['subject_id'],
        'subject_name' => $subject['subject_name']
    ];
}
$stmt->close();
?>