<?php
session_start();
include '../server/db_connect.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (empty($_POST)) {
        echo "<script>alert('Form data not received.');</script>";
        exit();
    }

    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    $email = trim($_POST['email']);
    $password = trim($_POST['password']);
    $phone = trim($_POST['phone']);
    $role_name = trim($_POST['role']);

    // Validate role - only allow student, teacher, worker
    if (!in_array($role_name, ['student', 'teacher', 'worker'])) {
        echo "<script>alert('Invalid role selected.'); window.history.back();</script>";
        exit();
    }

    if (empty($first_name) || empty($last_name) || empty($email) || empty($password) || empty($phone) || empty($role_name)) {
        echo "<script>alert('Please fill out all fields.'); window.history.back();</script>";
        exit();
    }

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo "<script>alert('Please enter a valid email address.'); window.history.back();</script>";
        exit();
    }

    // Validate phone number
    if (!preg_match('/^[0-9]{10}$/', $phone)) {
        echo "<script>alert('Phone number must be exactly 10 digits and contain only numbers.'); window.history.back();</script>";
        exit();
    }

    // Validate password strength
    $password_errors = [];

    // Check length
    if (strlen($password) < 8) {
        $password_errors[] = "Password must be at least 8 characters long";
    }

    // Check for letters
    if (!preg_match('/[a-zA-Z]/', $password)) {
        $password_errors[] = "Password must include at least one letter";
    }

    // Check for numbers
    if (!preg_match('/\d/', $password)) {
        $password_errors[] = "Password must include at least one number";
    }

    // Check for special characters
    if (!preg_match('/[^a-zA-Z0-9]/', $password)) {
        $password_errors[] = "Password must include at least one special character";
    }

    // If there are password errors, show them and stop registration
    if (!empty($password_errors)) {
        echo "<script>alert('Password requirements not met: " . implode(", ", $password_errors) . "'); window.history.back();</script>";
        exit();
    }

    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

    // Check if email already exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $stmt->store_result();

    if ($stmt->num_rows > 0) {
        echo "<script>alert('Email already exists.'); window.history.back();</script>";
        exit();
    }
    $stmt->close();

    // Get role_id from role name
    $role_stmt = $conn->prepare("SELECT role_id FROM roles WHERE role_name = ?");
    $role_stmt->bind_param("s", $role_name);
    $role_stmt->execute();
    $role_stmt->bind_result($role_id);
    $role_stmt->fetch();
    $role_stmt->close();

    if (!$role_id) {
        echo "<script>alert('Invalid role selected.'); window.history.back();</script>";
        exit();
    }

    // Now update the query with the correct column names
    $stmt = $conn->prepare("INSERT INTO users (first_name, last_name, email, password, role_id) VALUES (?, ?, ?, ?, ?)");
    $stmt->bind_param("ssssi", $first_name, $last_name, $email, $hashed_password, $role_id);

    if ($stmt->execute()) {
        $user_id = $stmt->insert_id;

        // Role-specific inserts
        if ($role_name === "student") {
            $grade = $_POST['grade'] ?? null;
            $subject_id = $_POST['subject_id_student'] ?? null;
            if ($grade) {
                $stmt = $conn->prepare("INSERT INTO students (user_id, phone, grade) VALUES (?, ?, ?)");
                $stmt->bind_param("iss", $user_id, $phone, $grade);
                if ($stmt->execute()) {
                    // Get the student_id from the newly inserted record
                    $student_id = $conn->insert_id;

                    // If subject was selected, add to student_subjects
                    if ($subject_id) {
                        $stmt = $conn->prepare("INSERT INTO student_subjects (student_id, subject_id, payment_status) VALUES (?, ?, 'UNPAID')");
                        $stmt->bind_param("ii", $student_id, $subject_id);
                        $stmt->execute();
                    }

                    // Generate QR Code
                    $qrDir = $_SERVER['DOCUMENT_ROOT'] . "/gurukula_lms/assets/qrcodes/";
                    if (!file_exists($qrDir)) {
                        mkdir($qrDir, 0777, true);
                    }

                    // Set session variables for the success page
                    $_SESSION['show_qr'] = true;
                    $_SESSION['student_id'] = $student_id;

                    // Check if GD library is available
                    if (function_exists('imagecreate')) {
                        try {
                            // Generate QR code for student
                            require_once '../../libs/phpqrcode/phpqrcode/qrlib.php';
                            require_once '../../libs/tcpdf/tcpdf.php';

                            $qrText = "student_id=" . $student_id . "&token=" . md5($student_id . date('Ymd'));
                            $qrImage = $qrDir . "student_" . $student_id . ".png";
                            $pdfFile = $qrDir . "student_" . $student_id . ".pdf";

                            // Generate QR code
                            QRcode::png($qrText, $qrImage, QR_ECLEVEL_L, 5);

                            // Generate PDF
                            $pdf = new TCPDF();
                            $pdf->AddPage();
                            $pdf->SetFont('helvetica', '', 14);
                            $pdf->Write(0, "QR Code for Student ID: $student_id", '', 0, 'C', true, 0, false, false, 0);
                            $pdf->Image($qrImage, 70, 40, 70, 70, 'PNG');
                            $pdf->Output($pdfFile, 'F');

                            $_SESSION['qr_image'] = "/gurukula_lms/assets/qrcodes/student_" . $student_id . ".png";
                            $_SESSION['qr_pdf'] = "/gurukula_lms/assets/qrcodes/student_" . $student_id . ".pdf";
                        } catch (Exception $e) {
                            // Log the error but continue with registration
                            error_log("QR Code generation failed: " . $e->getMessage());
                            $_SESSION['qr_error'] = "QR code generation failed. Please enable GD library in PHP.";
                        }
                    } else {
                        // GD library is not available
                        $_SESSION['qr_error'] = "QR code generation requires the GD library. Please enable it in your PHP configuration.";
                    }

                    // Redirect to success page
                    header("Location: registration_success.php");
                    exit();
                }
            }
        } elseif ($role_name === "teacher") {
            $subject_id = $_POST['subject_id_teacher'] ?? null;
            $stmt = $conn->prepare("INSERT INTO teachers (user_id, phone) VALUES (?, ?)");
            $stmt->bind_param("is", $user_id, $phone);
            if ($stmt->execute()) {
                $teacher_id = $conn->insert_id;

                // If subject was selected, add to teacher_subjects
                if ($subject_id) {
                    $stmt = $conn->prepare("INSERT INTO teacher_subjects (teacher_id, subject_id) VALUES (?, ?)");
                    $stmt->bind_param("ii", $teacher_id, $subject_id);
                    $stmt->execute();
                }
            }
        } elseif ($role_name === "worker") {
            $stmt = $conn->prepare("INSERT INTO workers (user_id, phone) VALUES (?, ?)");
            $stmt->bind_param("is", $user_id, $phone);
            $stmt->execute();
        }

        // For non-student roles, redirect to login page
        echo "<script>alert('Registration successful!'); window.location.href='../../frontend/login.php';</script>";
    } else {
        echo "<script>alert('Error registering user.');</script>";
    }

    $stmt->close();
    $conn->close();
}

// Prepare subjects for dropdown
$subjects_html = '';
$result = $conn->query("SELECT * FROM subjects");
while ($row = $result->fetch_assoc()) {
    $subjects_html .= "<option value='{$row['subject_id']}'>{$row['subject_name']}</option>";
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gurukula LMS Registration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f4f6f9;
            --dark-text: #2d3748;
            --light-text: #718096;
            --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --border-radius: 12px;
            --transition-speed: 0.3s;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 0;
            color: var(--dark-text);
        }

        .registration-container {
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
        }

        .registration-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            overflow: hidden;
            position: relative;
        }

        .registration-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
        }

        .registration-header h2 {
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 2rem;
        }

        .registration-header p {
            opacity: 0.9;
            margin-bottom: 0;
        }

        .registration-body {
            padding: 2.5rem;
        }

        .form-label {
            font-weight: 500;
            color: var(--dark-text);
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border-radius: 8px;
            padding: 0.75rem 1rem;
            border: 1px solid #e2e8f0;
            font-size: 1rem;
            transition: all 0.2s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
        }

        .input-group-text {
            background-color: #f8fafc;
            border-color: #e2e8f0;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border: none;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: var(--hover-shadow);
        }

        .btn-success {
            background-color: var(--success-color);
            border: none;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .btn-success:hover {
            background-color: #27ae60;
            transform: translateY(-2px);
            box-shadow: var(--hover-shadow);
        }

        .role-selector {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .role-option {
            flex: 1;
            text-align: center;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 2px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .role-option:hover {
            border-color: var(--primary-color);
            background-color: rgba(67, 97, 238, 0.05);
        }

        .role-option.active {
            border-color: var(--primary-color);
            background-color: rgba(67, 97, 238, 0.1);
        }

        .role-option i {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .role-option h5 {
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .role-option p {
            color: var(--light-text);
            font-size: 0.9rem;
            margin-bottom: 0;
        }

        .form-section {
            margin-bottom: 2rem;
        }

        .form-section-title {
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e2e8f0;
            color: var(--primary-color);
        }

        .password-strength {
            height: 5px;
            background-color: #e2e8f0;
            border-radius: 5px;
            margin-top: 0.5rem;
            overflow: hidden;
        }

        .password-strength-bar {
            height: 100%;
            width: 0;
            transition: width 0.3s ease;
        }

        .password-feedback {
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        .login-link {
            text-align: center;
            margin-top: 1.5rem;
            font-size: 0.95rem;
        }

        .login-link a {
            color: var(--primary-color);
            font-weight: 500;
            text-decoration: none;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .registration-body {
                padding: 1.5rem;
            }

            .role-selector {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="registration-container">
        <div class="registration-card">
            <div class="registration-header">
                <h2>Join Gurukula LMS</h2>
                <p>Create your account to get started</p>
            </div>
            <div class="registration-body">
                <form action="register.php" method="POST" id="registrationForm">
                    <input type="hidden" name="role" id="role_input" value="">

                    <div class="form-section">
                        <h4 class="form-section-title">Select Your Role</h4>
                        <div class="role-selector">
                            <div class="role-option" data-role="student" onclick="selectRole('student', this)">
                                <i class="fas fa-user-graduate"></i>
                                <h5>Student</h5>
                                <p>Join as a student to access courses and learning materials</p>
                            </div>
                            <div class="role-option" data-role="teacher" onclick="selectRole('teacher', this)">
                                <i class="fas fa-chalkboard-teacher"></i>
                                <h5>Teacher</h5>
                                <p>Join as a teacher to create and manage courses</p>
                            </div>
                            <div class="role-option" data-role="worker" onclick="selectRole('worker', this)">
                                <i class="fas fa-user-tie"></i>
                                <h5>Worker</h5>
                                <p>Join as administrative staff to manage the institution</p>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h4 class="form-section-title">Personal Information</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="first_name" name="first_name" placeholder="Enter your first name" required>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="last_name" name="last_name" placeholder="Enter your last name" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                <input type="tel" class="form-control" id="phone" name="phone" placeholder="Enter 10 digit phone number" pattern="[0-9]{10}" maxlength="10" title="Phone number must be exactly 10 digits and contain only numbers" required>
                            </div>
                            <div class="form-text text-muted">
                                <small><i class="fas fa-info-circle me-1"></i> Phone number must be exactly 10 digits (numbers only)</small>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email address" required>
                            </div>
                            <div class="form-text text-muted">
                                <small><i class="fas fa-info-circle me-1"></i> Please enter a valid email address (e.g., <EMAIL>)</small>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" class="form-control" id="password" name="password" placeholder="Create a strong password" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength mt-2">
                                <div class="password-strength-bar" id="passwordStrengthBar"></div>
                            </div>
                            <div class="password-feedback" id="passwordFeedback"></div>
                            <div class="password-requirements mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i> Password must:
                                    <ul class="mb-0 ps-4 mt-1">
                                        <li>Be at least 8 characters long</li>
                                        <li>Include at least one letter</li>
                                        <li>Include at least one number</li>
                                        <li>Include at least one special character (e.g., !@#$%^&*)</li>
                                    </ul>
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Student Fields -->
                    <div class="form-section" id="student_fields" style="display: none;">
                        <h4 class="form-section-title">Student Information</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="grade" class="form-label">Grade</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-graduation-cap"></i></span>
                                    <select name="grade" id="grade" class="form-select">
                                        <option value="">-- Select Grade --</option>
                                        <?php
                                        for ($i = 6; $i <= 11; $i++) {
                                            echo "<option value='{$i}'>Grade {$i}</option>";
                                        }
                                        ?>
                                        <option value="">A/L</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="subject_id_student" class="form-label">Subject</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-book"></i></span>
                                    <select name="subject_id_student" id="subject_id_student" class="form-select">
                                        <option value="">-- Select Subject --</option>
                                        <?= $subjects_html ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i> A QR code for attendance will be generated automatically after registration is complete.
                        </div>

                    </div>

                    <!-- Teacher Fields -->
                    <div class="form-section" id="teacher_fields" style="display: none;">
                        <h4 class="form-section-title">Teacher Information</h4>
                        <div class="mb-3">
                            <label for="subject_id_teacher" class="form-label">Subject You Teach</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-book"></i></span>
                                <select name="subject_id_teacher" id="subject_id_teacher" class="form-select">
                                    <option value="">-- Select Subject --</option>
                                    <?= $subjects_html ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 mt-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i> Create Account
                        </button>
                    </div>
                </form>

                <div class="login-link">
                    Already have an account? <a href="login.php">Log in</a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Role selection
        function selectRole(role, element) {
            // Update hidden input
            document.getElementById('role_input').value = role;

            // Update UI
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('active');
            });
            element.classList.add('active');

            // Show/hide role-specific fields
            document.getElementById('student_fields').style.display = (role === 'student') ? 'block' : 'none';
            document.getElementById('teacher_fields').style.display = (role === 'teacher') ? 'block' : 'none';
        }

        // Password visibility toggle
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // Email validation
        document.getElementById('email').addEventListener('input', function() {
            const email = this.value;
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

            // Validate the email format
            if (emailRegex.test(email)) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else if (email.length > 0) {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-valid');
                this.classList.remove('is-invalid');
            }
        });

        // Phone number validation
        document.getElementById('phone').addEventListener('input', function() {
            const phone = this.value;

            // Remove any non-numeric characters
            this.value = phone.replace(/\D/g, '');

            // Validate the phone number format
            if (this.value.length === 10) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else if (this.value.length > 0) {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-valid');
                this.classList.remove('is-invalid');
            }
        });

        // Password strength meter
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrengthBar');
            const feedback = document.getElementById('passwordFeedback');

            // Calculate strength
            let strength = 0;
            let message = '';

            if (password.length > 0) {
                // Length check
                if (password.length >= 8) strength += 25;

                // Uppercase and lowercase check
                if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength += 25;

                // Number check
                if (password.match(/\d/)) strength += 25;

                // Special character check
                if (password.match(/[^a-zA-Z0-9]/)) strength += 25;

                // Update strength bar
                strengthBar.style.width = strength + '%';

                // Set color and message based on strength
                if (strength < 25) {
                    strengthBar.style.backgroundColor = '#e74c3c';
                    message = '<span class="text-danger">Very Weak</span>';
                } else if (strength < 50) {
                    strengthBar.style.backgroundColor = '#f39c12';
                    message = '<span class="text-warning">Weak</span>';
                } else if (strength < 75) {
                    strengthBar.style.backgroundColor = '#3498db';
                    message = '<span class="text-info">Medium</span>';
                } else {
                    strengthBar.style.backgroundColor = '#2ecc71';
                    message = '<span class="text-success">Strong</span>';
                }
            }

            feedback.innerHTML = message;
        });

        // Form validation
        document.getElementById('registrationForm').addEventListener('submit', function(e) {
            const role = document.getElementById('role_input').value;
            const password = document.getElementById('password').value;
            const phone = document.getElementById('phone').value;
            const email = document.getElementById('email').value;
            let isValid = true;
            let errorMessage = '';

            // Validate role selection
            if (!role) {
                isValid = false;
                errorMessage = 'Please select a role to continue.';
            }

            // Validate student-specific fields
            if (isValid && role === 'student') {
                const grade = document.getElementById('grade').value;
                if (!grade) {
                    isValid = false;
                    errorMessage = 'Please select a grade.';
                }
            }

            // Validate email format
            if (isValid) {
                // Regular expression for email validation
                const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                if (!emailRegex.test(email)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid email address.';
                }
            }

            // Validate phone number
            if (isValid) {
                // Check if phone number is exactly 10 digits and contains only numbers
                if (!/^[0-9]{10}$/.test(phone)) {
                    isValid = false;
                    errorMessage = 'Phone number must be exactly 10 digits and contain only numbers.';
                }
            }

            // Validate password
            if (isValid) {
                // Check password length
                if (password.length < 8) {
                    isValid = false;
                    errorMessage = 'Password must be at least 8 characters long.';
                }
                // Check for letters
                else if (!password.match(/[a-zA-Z]/)) {
                    isValid = false;
                    errorMessage = 'Password must include at least one letter.';
                }
                // Check for numbers
                else if (!password.match(/\d/)) {
                    isValid = false;
                    errorMessage = 'Password must include at least one number.';
                }
                // Check for special characters
                else if (!password.match(/[^a-zA-Z0-9]/)) {
                    isValid = false;
                    errorMessage = 'Password must include at least one special character (e.g., !@#$%^&*).';
                }
            }

            // Prevent form submission if validation fails
            if (!isValid) {
                e.preventDefault();
                alert(errorMessage);
            }
        });


    </script>
</body>
</html>
