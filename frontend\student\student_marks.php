<?php
session_start();
include_once '../../backend/server/db_connect.php';

// Check if user is logged in as student
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header("Location: ../../backend/server/login.php");
    exit();
}

// Get student ID
$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT student_id, grade FROM students WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $student_data = $result->fetch_assoc();
    $student_id = $student_data['student_id'];
    $grade = $student_data['grade'];
} else {
    // Handle error - student not found
    $student_id = 0;
    $grade = '';
}
$stmt->close();

// Get student name
$stmt = $conn->prepare("SELECT CONCAT(first_name, ' ', last_name) as student_name FROM users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$name_result = $stmt->get_result();
$student_name = $name_result->fetch_assoc()['student_name'];
$stmt->close();

// Get subjects the student is enrolled in
$subjects = [];
$stmt = $conn->prepare("
    SELECT s.subject_id, s.subject_name
    FROM subjects s
    JOIN student_subjects ss ON s.subject_id = ss.subject_id
    WHERE ss.student_id = ?
");
$stmt->bind_param("i", $student_id);
$stmt->execute();
$subjects_result = $stmt->get_result();
while ($row = $subjects_result->fetch_assoc()) {
    $subjects[] = $row;
}
$stmt->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Marks - Gurukula Institution</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f4f6f9;
            --dark-text: #2d3748;
            --light-text: #718096;
            --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        body {
            background: var(--light-bg);
            font-family: 'Poppins', sans-serif;
            color: var(--dark-text);
        }

        .content {
            padding: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--card-shadow);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 200%;
            background: rgba(255, 255, 255, 0.1);
            transform: rotate(30deg);
        }

        .subject-card {
            background: white;
            border-radius: 16px;
            box-shadow: var(--card-shadow);
            margin-bottom: 30px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .subject-card:hover {
            box-shadow: var(--hover-shadow);
        }

        .subject-card .card-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .subject-card .card-body {
            padding: 1.5rem;
        }

        .table {
            margin-bottom: 0;
        }

        .table th {
            font-weight: 600;
            color: var(--dark-text);
            padding: 1rem;
            border-bottom-width: 1px;
        }

        .table td {
            padding: 1rem;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background-color: rgba(67, 97, 238, 0.05);
        }

        .badge-marks {
            font-size: 0.9rem;
            padding: 0.5rem 0.75rem;
            border-radius: 50rem;
            font-weight: 600;
        }

        .excellent {
            background-color: rgba(46, 204, 113, 0.15);
            color: #27ae60;
        }

        .good {
            background-color: rgba(52, 152, 219, 0.15);
            color: #2980b9;
        }

        .average {
            background-color: rgba(243, 156, 18, 0.15);
            color: #f39c12;
        }

        .poor {
            background-color: rgba(231, 76, 60, 0.15);
            color: #e74c3c;
        }

        .no-marks {
            background-color: rgba(189, 195, 199, 0.15);
            color: #7f8c8d;
        }

        .summary-box {
            background: white;
            border-radius: 16px;
            box-shadow: var(--card-shadow);
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .summary-box h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .summary-box p {
            color: var(--light-text);
            margin-bottom: 0;
        }

        @media (max-width: 768px) {
            .content {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <a href="../dashboards/student_dashboard.php" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>

        <div class="page-header">
            <h2 class="mb-2"><i class="fas fa-chart-bar me-2"></i>My Marks</h2>
            <p class="mb-0">View your performance across all subjects and assignments</p>
        </div>

        <div class="row mb-4">
            <div class="col-md-4">
                <div class="summary-box">
                    <i class="fas fa-award mb-3 text-primary" style="font-size: 2rem;"></i>
                    <h3 id="avgMarks">-</h3>
                    <p>Average Marks</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="summary-box">
                    <i class="fas fa-check-circle mb-3 text-success" style="font-size: 2rem;"></i>
                    <h3 id="completedAssignments">-</h3>
                    <p>Completed Assignments</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="summary-box">
                    <i class="fas fa-book mb-3 text-info" style="font-size: 2rem;"></i>
                    <h3 id="totalSubjects"><?= count($subjects) ?></h3>
                    <p>Total Subjects</p>
                </div>
            </div>
        </div>

        <?php if (count($subjects) > 0): ?>
            <?php foreach ($subjects as $subject): ?>
                <div class="subject-card">
                    <div class="card-header">
                        <div>
                            <i class="fas fa-book-open me-2"></i>
                            <?= htmlspecialchars($subject['subject_name']) ?>
                        </div>
                        <span class="badge bg-white text-primary subject-avg-marks" data-subject-id="<?= $subject['subject_id'] ?>">-</span>
                    </div>
                    <div class="card-body">
                        <?php
                        // Get assignments and marks for this subject
                        // First, let's check if there are any submissions for this student in this subject
                        $check_stmt = $conn->prepare("
                            SELECT s.submission_id, s.marks, s.comments, s.submitted_at, a.assignment_id, a.title, a.deadline
                            FROM submissions s
                            JOIN assignments a ON s.assignment_id = a.assignment_id
                            WHERE s.student_id = ? AND a.subject_id = ?
                        ");
                        $check_stmt->bind_param("ii", $student_id, $subject['subject_id']);
                        $check_stmt->execute();
                        $submissions_result = $check_stmt->get_result();
                        $has_submissions = $submissions_result->num_rows > 0;

                        // Now get all assignments for this subject, including those without submissions
                        $stmt = $conn->prepare("
                            SELECT a.assignment_id, a.title, a.deadline, s.marks, s.comments, s.submitted_at
                            FROM assignments a
                            LEFT JOIN submissions s ON a.assignment_id = s.assignment_id AND s.student_id = ?
                            WHERE a.subject_id = ? AND a.grade = ?
                            ORDER BY a.deadline DESC
                        ");
                        $stmt->bind_param("iis", $student_id, $subject['subject_id'], $grade);
                        $stmt->execute();
                        $assignments_result = $stmt->get_result();

                        // Debug info
                        $debug_info = [
                            'student_id' => $student_id,
                            'subject_id' => $subject['subject_id'],
                            'subject_name' => $subject['subject_name'],
                            'grade' => $grade,
                            'has_submissions' => $has_submissions,
                            'submissions_count' => $submissions_result->num_rows,
                            'assignments_count' => $assignments_result->num_rows
                        ];

                        // If we have submissions, let's display them for debugging
                        if ($has_submissions) {
                            $submissions_data = [];
                            while ($sub = $submissions_result->fetch_assoc()) {
                                $submissions_data[] = [
                                    'title' => $sub['title'],
                                    'marks' => $sub['marks'],
                                    'comments' => $sub['comments']
                                ];
                            }
                            $debug_info['submissions'] = $submissions_data;

                            // Reset the result pointer
                            $submissions_result->data_seek(0);
                        }
                        ?>

                        <?php if ($assignments_result->num_rows > 0 || $has_submissions): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Assignment</th>
                                            <th>Deadline</th>
                                            <th>Submission Date</th>
                                            <th>Marks</th>
                                            <th>Feedback</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // If we have assignments from the main query
                                        if ($assignments_result->num_rows > 0) {
                                            while ($assignment = $assignments_result->fetch_assoc()):
                                        ?>
                                            <tr>
                                                <td><?= htmlspecialchars($assignment['title']) ?></td>
                                                <td><?= date("d M Y", strtotime($assignment['deadline'])) ?></td>
                                                <td>
                                                    <?= $assignment['submitted_at'] ? date("d M Y", strtotime($assignment['submitted_at'])) : '<span class="text-muted">Not submitted</span>' ?>
                                                </td>
                                                <td>
                                                    <?php if ($assignment['marks']): ?>
                                                        <?php
                                                        $marks = intval($assignment['marks']);
                                                        $class = '';
                                                        if ($marks >= 85) $class = 'excellent';
                                                        else if ($marks >= 70) $class = 'good';
                                                        else if ($marks >= 50) $class = 'average';
                                                        else $class = 'poor';
                                                        ?>
                                                        <span class="badge badge-marks <?= $class ?>"><?= $marks ?></span>
                                                    <?php else: ?>
                                                        <span class="badge badge-marks no-marks">Not graded</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?= $assignment['comments'] ? htmlspecialchars($assignment['comments']) : '<span class="text-muted">No feedback</span>' ?>
                                                </td>
                                            </tr>
                                        <?php
                                            endwhile;
                                        } elseif ($has_submissions) {
                                            // If we have submissions but no assignments from the main query
                                            while ($submission = $submissions_result->fetch_assoc()):
                                        ?>
                                            <tr>
                                                <td><?= htmlspecialchars($submission['title']) ?></td>
                                                <td><?= date("d M Y", strtotime($submission['deadline'])) ?></td>
                                                <td>
                                                    <?= $submission['submitted_at'] ? date("d M Y", strtotime($submission['submitted_at'])) : '<span class="text-muted">Not submitted</span>' ?>
                                                </td>
                                                <td>
                                                    <?php if ($submission['marks']): ?>
                                                        <?php
                                                        $marks = intval($submission['marks']);
                                                        $class = '';
                                                        if ($marks >= 85) $class = 'excellent';
                                                        else if ($marks >= 70) $class = 'good';
                                                        else if ($marks >= 50) $class = 'average';
                                                        else $class = 'poor';
                                                        ?>
                                                        <span class="badge badge-marks <?= $class ?>"><?= $marks ?></span>
                                                    <?php else: ?>
                                                        <span class="badge badge-marks no-marks">Not graded</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?= $submission['comments'] ? htmlspecialchars($submission['comments']) : '<span class="text-muted">No feedback</span>' ?>
                                                </td>
                                            </tr>
                                        <?php
                                            endwhile;
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-info-circle text-muted mb-3" style="font-size: 2rem;"></i>
                                <p class="mb-0">No assignments found for this subject.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                You are not enrolled in any subjects yet. Please add subjects to view your marks.
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            console.log("Calculating statistics...");

            // Calculate statistics
            let totalMarks = 0;
            let marksCount = 0;
            let completedAssignments = 0;

            // Process each subject card
            $('.subject-card').each(function() {
                const subjectCard = $(this);
                const subjectId = subjectCard.find('.subject-avg-marks').data('subject-id');
                const subjectName = subjectCard.find('.card-header div').text().trim();

                console.log("Processing subject: " + subjectName + " (ID: " + subjectId + ")");

                let subjectTotal = 0;
                let subjectCount = 0;
                let subjectCompletedAssignments = 0;

                // Find all rows in the table
                subjectCard.find('tbody tr').each(function() {
                    const row = $(this);
                    const marksCell = row.find('td:nth-child(4)'); // 4th column is marks
                    const submissionCell = row.find('td:nth-child(3)'); // 3rd column is submission date

                    // Check if this assignment has been submitted
                    if (submissionCell.length > 0 &&
                        !submissionCell.text().includes('Not submitted') &&
                        submissionCell.text().trim() !== '') {

                        subjectCompletedAssignments++;
                        completedAssignments++;
                        console.log("Found completed assignment in " + subjectName);

                        // Check if this assignment has been graded
                        if (marksCell.length > 0) {
                            const marksBadge = marksCell.find('.badge-marks');
                            if (marksBadge.length > 0) {
                                const text = marksBadge.text().trim();
                                if (text !== 'Not graded') {
                                    const mark = parseInt(text);
                                    if (!isNaN(mark)) {
                                        subjectTotal += mark;
                                        subjectCount++;
                                        totalMarks += mark;
                                        marksCount++;
                                        console.log("Found valid mark in " + subjectName + ": " + mark);
                                    }
                                }
                            }
                        }
                    }
                });

                // Update subject average
                if (subjectCount > 0) {
                    const subjectAvg = Math.round(subjectTotal / subjectCount);
                    subjectCard.find('.subject-avg-marks').text(subjectAvg);
                    console.log(subjectName + " average: " + subjectAvg);
                } else {
                    subjectCard.find('.subject-avg-marks').text('N/A');
                }
            });

            // Update summary statistics
            if (marksCount > 0) {
                const avgMarks = Math.round(totalMarks / marksCount);
                $('#avgMarks').text(avgMarks);
                console.log("Overall average marks: " + avgMarks);

                // Set color based on performance
                if (avgMarks >= 85) {
                    $('#avgMarks').addClass('text-success');
                } else if (avgMarks >= 70) {
                    $('#avgMarks').addClass('text-info');
                } else if (avgMarks >= 50) {
                    $('#avgMarks').addClass('text-warning');
                } else {
                    $('#avgMarks').addClass('text-danger');
                }
            } else {
                $('#avgMarks').text('N/A');
            }

            $('#completedAssignments').text(completedAssignments);
            console.log("Completed assignments: " + completedAssignments);

            // Add debug info to the console
            console.log("Total marks: " + totalMarks);
            console.log("Marks count: " + marksCount);
            console.log("Completed assignments: " + completedAssignments);
        });
    </script>
</body>
</html>
