<?php
session_start();
include '../server/db_connect.php';

// Check if user is logged in and is a teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../../frontend/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get teacher_id from user_id
$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();
$teacher_id = $teacher['teacher_id'] ?? null;
$stmt->close();

if ($_SERVER['REQUEST_METHOD'] === 'POST' && $teacher_id) {
    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    $email = trim($_POST['email']);

    if (!empty($first_name) && !empty($last_name) && !empty($email)) {
        // Update the users table with the new information
        $sql = "UPDATE users SET first_name = ?, last_name = ?, email = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sssi", $first_name, $last_name, $email, $user_id);

        if ($stmt->execute()) {
            echo "<script>alert('Profile updated successfully'); window.location.href='../../frontend/settings/teacher_settings.php';</script>";
        } else {
            echo "<script>alert('Update failed. Try again.'); window.location.href='../../frontend/settings/teacher_settings.php';</script>";
        }
    } else {
        echo "<script>alert('First name, last name, and email are all required.'); window.location.href='../../frontend/settings/teacher_settings.php';</script>";
    }
} else {
    header("Location: ../../frontend/settings/teacher_settings.php");
    exit();
}
?>
