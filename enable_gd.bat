@echo off
echo Enabling GD Library in PHP...
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This script requires administrator privileges.
    echo Please right-click on this file and select "Run as administrator".
    echo.
    pause
    exit /b
)

REM Path to php.ini
set PHP_INI=C:\xampp\php\php.ini

REM Check if php.ini exists
if not exist "%PHP_INI%" (
    echo Error: php.ini not found at %PHP_INI%
    echo.
    pause
    exit /b
)

REM Create a temporary file
set TEMP_FILE=%TEMP%\php.ini.tmp

REM Process the file
echo Processing php.ini...
set FOUND=0
set ALREADY_ENABLED=0

for /f "tokens=*" %%a in ('type "%PHP_INI%"') do (
    set LINE=%%a
    if "!LINE!"==";extension=gd" (
        echo extension=gd>>"%TEMP_FILE%"
        echo Found and enabled GD extension.
        set FOUND=1
    ) else if "!LINE!"=="extension=gd" (
        echo !LINE!>>"%TEMP_FILE%"
        echo GD extension is already enabled.
        set ALREADY_ENABLED=1
    ) else (
        echo !LINE!>>"%TEMP_FILE%"
    )
)

if %ALREADY_ENABLED%==1 (
    echo.
    echo GD library is already enabled in php.ini.
    echo No changes were made.
    del "%TEMP_FILE%"
) else if %FOUND%==1 (
    echo.
    echo Successfully enabled GD library.
    copy /y "%TEMP_FILE%" "%PHP_INI%" >nul
    del "%TEMP_FILE%"
) else (
    echo.
    echo Could not find the GD extension line in php.ini.
    echo Please edit the file manually:
    echo 1. Open %PHP_INI% in a text editor
    echo 2. Find the line ;extension=gd
    echo 3. Remove the semicolon (;) so it becomes extension=gd
    echo 4. Save the file
    del "%TEMP_FILE%"
)

echo.
echo Please restart Apache in XAMPP Control Panel.
echo.
pause
