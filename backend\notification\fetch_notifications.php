<?php
session_start();
// Fix the include path based on how the script is called
$base_path = __DIR__ . '/../../';
include_once $base_path . 'backend/server/db_connect.php';

// Add debugging
error_log("Fetching notifications for user");

header('Content-Type: application/json');

$user_id = $_SESSION['user_id'] ?? 0;
$role = $_SESSION['role'] ?? '';

error_log("User ID: $user_id, Role: $role");

if ($role !== 'teacher') {
    error_log("Not a teacher, returning empty array");
    echo json_encode([]);
    exit();
}

// Get teacher ID
$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$res = $stmt->get_result();
$teacher = $res->fetch_assoc();
$teacher_id = $teacher['teacher_id'] ?? 0;

error_log("Teacher ID: $teacher_id");

$data = [];
if ($teacher_id) {
    // Check if notifications table exists
    $tableExists = false;
    $result = $conn->query("SHOW TABLES LIKE 'notifications'");
    if ($result->num_rows > 0) {
        $tableExists = true;
    }

    if (!$tableExists) {
        // If notifications table doesn't exist, create it
        $sql = "CREATE TABLE IF NOT EXISTS notifications (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            teacher_id INT(11) NOT NULL,
            message VARCHAR(255) NOT NULL,
            is_read TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            scheduled_time TIMESTAMP NULL
        )";

        if ($conn->query($sql) === TRUE) {
            error_log("Notifications table created successfully");
        } else {
            error_log("Error creating notifications table: " . $conn->error);
        }
    }

    // Check if there are any notifications for this teacher
    $q = $conn->prepare("SELECT id, message, created_at, scheduled_time FROM notifications WHERE teacher_id = ? AND is_read = 0");
    $q->bind_param("i", $teacher_id);
    $q->execute();
    $r = $q->get_result();

    error_log("Found " . $r->num_rows . " unread notifications");

    while ($row = $r->fetch_assoc()) {
        $data[] = $row;
        error_log("Notification: " . $row['message']);
    }

    // If no notifications found, check for unanswered questions and create notifications
    if (count($data) == 0) {
        error_log("No notifications found, checking for unanswered questions");

        // Get subjects taught by this teacher
        $subjectQuery = $conn->prepare("
            SELECT subject_id FROM teacher_subjects WHERE teacher_id = ?
        ");
        $subjectQuery->bind_param("i", $teacher_id);
        $subjectQuery->execute();
        $subjectResult = $subjectQuery->get_result();

        $subjectIds = [];
        while ($subject = $subjectResult->fetch_assoc()) {
            $subjectIds[] = $subject['subject_id'];
        }

        if (count($subjectIds) > 0) {
            error_log("Teacher teaches " . count($subjectIds) . " subjects");

            // Get unanswered questions for these subjects
            $placeholders = str_repeat('?,', count($subjectIds) - 1) . '?';
            $questionQuery = $conn->prepare("
                SELECT q.id, q.student_id, q.subject_id, q.question_text, q.created_at, s.subject_name
                FROM questions q
                JOIN subjects s ON q.subject_id = s.subject_id
                WHERE q.subject_id IN ($placeholders)
                AND (q.reply_text IS NULL OR q.reply_text = '')
                ORDER BY q.created_at DESC
                LIMIT 5
            ");

            $types = str_repeat('i', count($subjectIds));
            $questionQuery->bind_param($types, ...$subjectIds);
            $questionQuery->execute();
            $questionResult = $questionQuery->get_result();

            error_log("Found " . $questionResult->num_rows . " unanswered questions");

            // Create notifications for unanswered questions
            while ($question = $questionResult->fetch_assoc()) {
                $subjectName = $question['subject_name'];
                $message = "New question in $subjectName needs your attention";

                // Insert notification
                $insertQuery = $conn->prepare("
                    INSERT INTO notifications (teacher_id, message, created_at)
                    VALUES (?, ?, ?)
                ");
                $insertQuery->bind_param("iss", $teacher_id, $message, $question['created_at']);

                if ($insertQuery->execute()) {
                    $notificationId = $conn->insert_id;
                    $data[] = [
                        'id' => $notificationId,
                        'message' => $message,
                        'created_at' => $question['created_at'],
                        'scheduled_time' => null
                    ];
                    error_log("Created notification: $message");
                }
            }
        }
    }
}

echo json_encode($data);
?>
