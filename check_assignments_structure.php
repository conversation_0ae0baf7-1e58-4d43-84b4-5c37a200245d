<?php
// Include database connection
include_once 'backend/server/db_connect.php';

// Check if assignments table exists
$result = $conn->query("SHOW TABLES LIKE 'assignments'");
if ($result->num_rows == 0) {
    echo "Assignments table does not exist!";
    exit;
}

// Get table structure
$result = $conn->query("DESCRIBE assignments");
echo "<h2>Assignments Table Structure</h2>";
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "<td>" . $row['Extra'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check for sample data
$result = $conn->query("SELECT * FROM assignments LIMIT 5");
echo "<h2>Sample Data</h2>";
if ($result->num_rows > 0) {
    echo "<table border='1'>";
    $first = true;
    while ($row = $result->fetch_assoc()) {
        if ($first) {
            echo "<tr>";
            foreach ($row as $key => $value) {
                echo "<th>" . $key . "</th>";
            }
            echo "</tr>";
            $first = false;
        }
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . $value . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No data found in assignments table.";
}

// Check for subject_id column
$result = $conn->query("SHOW COLUMNS FROM assignments LIKE 'subject_id'");
if ($result->num_rows == 0) {
    echo "<h2>Error: 'subject_id' column does not exist in assignments table!</h2>";
    
    // Add the subject_id column
    echo "<h3>Adding 'subject_id' column to assignments table...</h3>";
    if ($conn->query("ALTER TABLE assignments ADD COLUMN subject_id INT NULL AFTER teacher_id")) {
        echo "Successfully added 'subject_id' column!";
    } else {
        echo "Error adding 'subject_id' column: " . $conn->error;
    }
}

$conn->close();
?>
