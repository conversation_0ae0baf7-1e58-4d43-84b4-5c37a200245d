<?php
session_start();
include_once 'backend/server/db_connect.php';

// Check if user is logged in as teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get teacher_id
$stmt = $conn->prepare("SELECT teacher_id, first_name, last_name FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_id = null;
$teacher_name = "";
if ($row = $result->fetch_assoc()) {
    $teacher_id = $row['teacher_id'];
    $teacher_name = $row['first_name'] . ' ' . $row['last_name'];
}
$stmt->close();

echo "<h1>Current Teacher Subjects Debug</h1>";
echo "<p>Teacher ID: $teacher_id</p>";
echo "<p>Teacher Name: $teacher_name</p>";
echo "<p>User ID: $user_id</p>";

// Get raw data from teacher_subjects table
echo "<h2>Raw Data from teacher_subjects Table</h2>";
$stmt = $conn->prepare("
    SELECT ts.id, ts.teacher_id, ts.subject_id, s.subject_name
    FROM teacher_subjects ts
    JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE ts.teacher_id = ?
    ORDER BY s.subject_name
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();

echo "<table border='1'>";
echo "<tr><th>ID</th><th>Teacher ID</th><th>Subject ID</th><th>Subject Name</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . $row['teacher_id'] . "</td>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "<td>" . $row['subject_name'] . "</td>";
    echo "</tr>";
}
echo "</table>";
$stmt->close();

// Get data with DISTINCT
echo "<h2>Data with DISTINCT</h2>";
$stmt = $conn->prepare("
    SELECT DISTINCT ts.subject_id, s.subject_name
    FROM teacher_subjects ts
    JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE ts.teacher_id = ?
    ORDER BY s.subject_name
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();

echo "<table border='1'>";
echo "<tr><th>Subject ID</th><th>Subject Name</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "<td>" . $row['subject_name'] . "</td>";
    echo "</tr>";
}
echo "</table>";
$stmt->close();

// Get data with the subquery approach used in manage_fees.php
echo "<h2>Data with Subquery Approach (used in manage_fees.php)</h2>";
$stmt = $conn->prepare("
    SELECT s.subject_id, s.subject_name
    FROM subjects s
    WHERE s.subject_id IN (
        SELECT DISTINCT ts.subject_id
        FROM teacher_subjects ts
        WHERE ts.teacher_id = ?
    )
    ORDER BY s.subject_name
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();

echo "<table border='1'>";
echo "<tr><th>Subject ID</th><th>Subject Name</th></tr>";
$teacher_subjects = [];
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "<td>" . $row['subject_name'] . "</td>";
    echo "</tr>";
    
    // Store for later processing
    $teacher_subjects[] = [
        'subject_id' => $row['subject_id'],
        'subject_name' => $row['subject_name']
    ];
}
echo "</table>";
$stmt->close();

// Debug the subject class generation
echo "<h2>Subject Class Generation Debug</h2>";
echo "<table border='1'>";
echo "<tr><th>Subject Name</th><th>Generated Class</th><th>Final Class</th></tr>";

foreach ($teacher_subjects as $subject) {
    $subject_name = $subject['subject_name'];
    $subject_class = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $subject_name));
    
    // Map subject names directly to their classes to avoid issues
    $subject_class_map = [
        'English' => 'english',
        'Science' => 'science',
        'Mathematics' => 'mathematics',
    ];
    
    // Use the mapping if available, otherwise use the generated class
    if (isset($subject_class_map[$subject_name])) {
        $final_class = $subject_class_map[$subject_name];
    } else {
        $valid_subjects = ['english', 'science', 'mathematics'];
        $final_class = in_array($subject_class, $valid_subjects) ? $subject_class : 'default';
    }
    
    echo "<tr>";
    echo "<td>" . $subject_name . "</td>";
    echo "<td>" . $subject_class . "</td>";
    echo "<td>" . $final_class . "</td>";
    echo "</tr>";
}
echo "</table>";

// Add a link to go back to manage_fees.php
echo "<p><a href='frontend/teacher/manage_fees.php'>Go to Manage Fees Page</a></p>";
echo "<p><a href='frontend/teacher/view_all_subjects.php'>Go to View All Subjects Page</a></p>";

$conn->close();
?>
