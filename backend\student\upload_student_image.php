<?php
session_start();
include '../../backend/server/db_connect.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['profile_image'])) {
    $user_id = $_SESSION['user_id'];
    $uploadDir = '../../uploads/profiles/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    $filename = time() . '_' . $_FILES['profile_image']['name'];
    $targetPath = $uploadDir . $filename;
    $relativePath = 'uploads/profiles/' . $filename;

    if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $targetPath)) {
        // Make sure the relative path is correct
        $relativePath = 'uploads/profiles/' . $filename;

        // Update the database with the correct path
        $stmt = $conn->prepare("UPDATE students SET profile_image = ? WHERE user_id = ?");
        $stmt->bind_param("si", $relativePath, $user_id);
        $stmt->execute();

        // Verify the update was successful
        if ($stmt->affected_rows > 0) {
            $_SESSION['upload_success'] = true;
        } else {
            $_SESSION['upload_error'] = "Database update failed. Please try again.";
        }

        $stmt->close();
    } else {
        $_SESSION['upload_error'] = "Failed to upload image. Please try again.";
    }
}
// Add debugging to verify the image path
if (isset($_SESSION['debug'])) {
    echo "Image uploaded to: " . $relativePath;
    echo "<br>User ID: " . $user_id;
    echo "<pre>";
    print_r($_FILES);
    echo "</pre>";
    exit();
}

// Redirect back to the student dashboard
header("Location: ../../frontend/dashboards/student_dashboard.php");
exit();
?>
