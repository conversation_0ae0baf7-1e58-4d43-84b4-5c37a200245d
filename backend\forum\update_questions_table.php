<?php
// This script adds the is_new_reply and is_notified columns to the questions table
require_once '../server/db_connect.php';

// Check if the columns already exist
$result = $conn->query("SHOW COLUMNS FROM questions LIKE 'is_new_reply'");
$is_new_reply_exists = $result->num_rows > 0;

$result = $conn->query("SHOW COLUMNS FROM questions LIKE 'is_notified'");
$is_notified_exists = $result->num_rows > 0;

// Add the is_new_reply column if it doesn't exist
if (!$is_new_reply_exists) {
    $sql = "ALTER TABLE questions ADD COLUMN is_new_reply TINYINT(1) DEFAULT 0";
    if ($conn->query($sql) === TRUE) {
        echo "Column is_new_reply added successfully<br>";
    } else {
        echo "Error adding column is_new_reply: " . $conn->error . "<br>";
    }
}

// Add the is_notified column if it doesn't exist
if (!$is_notified_exists) {
    $sql = "ALTER TABLE questions ADD COLUMN is_notified TINYINT(1) DEFAULT 0";
    if ($conn->query($sql) === TRUE) {
        echo "Column is_notified added successfully<br>";
    } else {
        echo "Error adding column is_notified: " . $conn->error . "<br>";
    }
}

echo "Database update complete";
?>
