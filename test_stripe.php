<?php
// Test file to verify <PERSON><PERSON> is working correctly
require 'vendor/autoload.php';

// Set your Stripe API key
$stripe = new \Stripe\StripeClient('sk_test_51RLOlhFjMkGP5dsLeAmPlAvrGl7oNq5gvqijfgtFIjBYfbfgKsmvv3utPbL3z2V3FDtEgfaCry3ATh0dTTRiZfPP00vUkW9DW9');

try {
    // Try to fetch a simple Stripe resource to verify the connection
    $balance = $stripe->balance->retrieve();
    echo "<h1>Stripe Connection Successful!</h1>";
    echo "<pre>";
    print_r($balance);
    echo "</pre>";
} catch (\Exception $e) {
    echo "<h1>Stripe Connection Failed</h1>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
