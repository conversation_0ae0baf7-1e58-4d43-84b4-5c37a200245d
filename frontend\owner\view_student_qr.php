<?php
if (!isset($student_id)) {
    exit("No student ID provided.");
}

require_once '../../libs/phpqrcode/phpqrcode/qrlib.php';
require_once '../../libs/tcpdf/tcpdf.php';

$qrText = "student_id=" . $student_id . "&token=" . md5($student_id . date('Ymd'));
$qrDir = realpath(__DIR__ . '/../../assets/qrcodes');
if (!$qrDir) {
    $qrDir = __DIR__ . '/../../assets/qrcodes';
    mkdir($qrDir, 0777, true);
}

$qrImage = $qrDir . DIRECTORY_SEPARATOR . "student_$student_id.png";
$pdfFile = $qrDir . DIRECTORY_SEPARATOR . "student_$student_id.pdf";

// Generate QR
QRcode::png($qrText, $qrImage, QR_ECLEVEL_L, 5);

// Generate PDF
$pdf = new TCPDF();
$pdf->AddPage();
$pdf->SetFont('helvetica', '', 14);
$pdf->Write(0, "QR Code for Student ID: $student_id", '', 0, 'C', true, 0, false, false, 0);
$pdf->Image($qrImage, 70, 40, 70, 70, 'PNG');
$pdf->Output($pdfFile, 'F');


// Generate relative paths for web display
$webQrImage = '../../assets/qrcodes/' . "student_$student_id.png";
$webPdfFile = '../../assets/qrcodes/' . "student_$student_id.pdf";
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>QR Code PDF</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light text-center p-5">

  <h3 class="mb-4">QR PDF Created Successfully</h3>
  <img src="<?= $webQrImage ?>" alt="QR Code" class="img-thumbnail mb-3" width="200">
  <br>
  <a href="<?= $webPdfFile ?>" class="btn btn-success btn-lg" download>Download QR PDF</a>

</body>
</html>
