@echo off
setlocal enabledelayedexpansion

echo GD Library Fix Tool
echo =================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script requires administrator privileges.
    echo Please right-click on this file and select "Run as administrator".
    echo.
    pause
    exit /b 1
)

REM Find php.ini file
set PHP_INI=C:\xampp\php\php.ini
if not exist "%PHP_INI%" (
    echo ERROR: Cannot find php.ini at %PHP_INI%
    echo.
    pause
    exit /b 1
)

echo Found php.ini at: %PHP_INI%
echo.

REM Create backup
set BACKUP=%PHP_INI%.bak
copy /Y "%PHP_INI%" "%BACKUP%" >nul
echo Created backup at: %BACKUP%
echo.

REM Check if GD is already enabled
findstr /C:"extension=gd" "%PHP_INI%" >nul
if %errorLevel% equ 0 (
    echo GD extension is already enabled in php.ini.
    echo.
    
    REM Check if it's commented out
    findstr /C:";extension=gd" "%PHP_INI%" >nul
    if %errorLevel% equ 0 (
        echo But it's commented out. Uncommenting...
        
        REM Uncomment the GD extension
        powershell -Command "(Get-Content '%PHP_INI%') -replace ';extension=gd', 'extension=gd' | Set-Content '%PHP_INI%'"
        
        echo Done! GD extension has been enabled.
    ) else (
        echo But you're still having issues. Let's try reinstalling it.
        
        REM Try reinstalling by commenting and uncommenting
        powershell -Command "(Get-Content '%PHP_INI%') -replace 'extension=gd', ';extension=gd' | Set-Content '%PHP_INI%'"
        powershell -Command "(Get-Content '%PHP_INI%') -replace ';extension=gd', 'extension=gd' | Set-Content '%PHP_INI%'"
        
        echo Done! GD extension has been reinstalled.
    )
) else (
    echo GD extension not found in php.ini.
    echo Adding it to the extensions section...
    
    REM Find the extensions section
    powershell -Command "(Get-Content '%PHP_INI%') -replace ';extension=exif', ';extension=exif\nextension=gd' | Set-Content '%PHP_INI%'"
    
    echo Done! GD extension has been added.
)

echo.
echo IMPORTANT: You must restart Apache in XAMPP Control Panel for changes to take effect.
echo.
echo Steps:
echo 1. Open XAMPP Control Panel
echo 2. Click "Stop" next to Apache
echo 3. Click "Start" next to Apache
echo 4. Try registering a student again
echo.

pause
