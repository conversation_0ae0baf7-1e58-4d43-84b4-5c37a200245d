<?php
$currentPage = basename($_SERVER['PHP_SELF']); // e.g. subject_details.php
?>

<!-- Mobile menu toggle button -->
<button class="mobile-menu-toggle d-md-none">
  <i class="fas fa-bars"></i>
</button>

<div class="sidebar">
    <h3><i class="fa-solid fa-bars"></i> <span>Gurukula</span></h3>
    <a href="../dashboards/teacher_dashboard.php" class="<?= $currentPage === 'teacher_dashboard.php' ? 'active' : '' ?>">
      <i class="fas fa-home"></i> <span>Dashboard</span>
    </a>
    <a href="../teacher/subject_details.php" class="<?= $currentPage === 'subject_details.php' ? 'active' : '' ?>">
      <i class="fa-solid fa-book-open"></i> <span>Subject Details</span>
    </a>
    <a href="../teacher/assignments.php" class="<?= $currentPage === 'assignments.php' ? 'active' : '' ?>">
      <i class="fa-solid fa-book"></i> <span>Assignments</span>
    </a>
    <a href="../teacher/teacher_view_submissions.php" class="<?= $currentPage === 'teacher_view_submissions.php' ? 'active' : '' ?>">
      <i class="fa-regular fa-star"></i> <span>Grading</span>
    </a>
    <a href="../forum/teacher_forum.php" class="<?= $currentPage === 'teacher_forum.php' ? 'active' : '' ?>">
      <i class="fas fa-comments"></i> <span>Forum</span>
    </a>
    <a href="../timetables/teacher_timetable.php" class="<?= $currentPage === 'teacher_timetable.php' ? 'active' : '' ?>">
      <i class="fa-solid fa-calendar-minus"></i> <span>Time Table</span>
    </a>
    <a href="../teacher/view_all_subjects.php" class="<?= $currentPage === 'view_all_subjects.php' ? 'active' : '' ?>">
      <i class="fa-solid fa-list"></i> <span>All Subjects</span>
    </a>
    <a href="../teacher/manage_fees.php" class="<?= $currentPage === 'manage_fees.php' ? 'active' : '' ?>">
      <i class="fa-solid fa-money-bill-wave"></i> <span>Manage Fees</span>
    </a>
    <a href="../settings/teacher_settings.php" class="<?= $currentPage === 'teacher_settings.php' ? 'active' : '' ?>">
      <i class="fa-solid fa-gear"></i> <span>Settings</span>
    </a>
    <a href="../home.php" class="text-danger">
      <i class="fas fa-sign-out-alt"></i> <span>Logout</span>
    </a>
</div>

<!-- Include responsive CSS and JS -->
<link rel="stylesheet" href="../../assets/css/responsive.css">
<script src="../../assets/js/responsive-nav.js" defer></script>

<style>
  /* Sidebar styles */
  .sidebar {
    height: 100vh;
    width: 260px;
    position: fixed;
    top: 0;
    left: 0;
    background-color: #02005F;
    padding: 25px;
    color: #fff;
    transition: all 0.3s;
    z-index: 1040;
  }
  .sidebar h3 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 1.8rem;
  }
  .sidebar a {
    color: #fff;
    text-decoration: none;
    display: block;
    padding: 12px;
    margin-bottom: 12px;
    border-radius: 8px;
    font-size: 1.2rem;
    transition: 0.3s;
  }
  .sidebar a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
  }
  .sidebar a:hover,
  .sidebar a.active {
    background-color: #67BAFD;
    transform: scale(1.05);
  }
</style>