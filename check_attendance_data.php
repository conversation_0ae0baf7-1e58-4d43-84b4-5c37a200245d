<?php
// Include database connection
include_once 'backend/server/db_connect.php';

// Set headers for better readability
header('Content-Type: text/html; charset=utf-8');

echo "<h2>Attendance Table Structure</h2>";
$result = $conn->query("DESCRIBE attendance");
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "<td>" . $row['Extra'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check for sample data
echo "<h2>Sample Attendance Data</h2>";
$result = $conn->query("SELECT * FROM attendance ORDER BY date DESC LIMIT 20");
if ($result->num_rows > 0) {
    echo "<table border='1'>";
    $first = true;
    while ($row = $result->fetch_assoc()) {
        if ($first) {
            echo "<tr>";
            foreach ($row as $key => $value) {
                echo "<th>" . $key . "</th>";
            }
            echo "</tr>";
            $first = false;
        }
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . $value . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No data found in attendance table.";
}

// Check weekly attendance data
echo "<h2>Weekly Attendance Counts</h2>";
$current_month = date('m');
$current_year = date('Y');

$query = "
    SELECT 
        week,
        COUNT(DISTINCT student_id) as student_count
    FROM 
        attendance
    WHERE 
        MONTH(date) = ? AND YEAR(date) = ?
    GROUP BY 
        week
";

$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $current_month, $current_year);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>Week</th><th>Student Count</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['week'] . "</td>";
        echo "<td>" . $row['student_count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No weekly attendance data found for the current month.";
}

$conn->close();
?>
