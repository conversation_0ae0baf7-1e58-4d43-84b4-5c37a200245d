<?php
session_start();
include_once 'backend/server/db_connect.php';

// Check if user is logged in as teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    echo "Please log in as a teacher to view this page.";
    exit();
}

$user_id = $_SESSION['user_id'];

// Get teacher_id
$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_id = null;
if ($row = $result->fetch_assoc()) {
    $teacher_id = $row['teacher_id'];
}
$stmt->close();

echo "<h1>Teacher Subject Debug Information</h1>";
echo "<p>Teacher ID: $teacher_id</p>";
echo "<p>User ID: $user_id</p>";

// Get all subjects this teacher teaches (raw data)
$stmt = $conn->prepare("
    SELECT ts.subject_id, s.subject_name
    FROM teacher_subjects ts
    JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE ts.teacher_id = ?
    ORDER BY s.subject_name
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();

echo "<h2>All Teacher-Subject Relationships (Raw Data)</h2>";
echo "<table border='1'>";
echo "<tr><th>Subject ID</th><th>Subject Name</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "<td>" . $row['subject_name'] . "</td>";
    echo "</tr>";
}
echo "</table>";
$stmt->close();

// Get all subjects from the subjects table
$result = $conn->query("SELECT subject_id, subject_name FROM subjects ORDER BY subject_name");

echo "<h2>All Subjects in Database</h2>";
echo "<table border='1'>";
echo "<tr><th>Subject ID</th><th>Subject Name</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "<td>" . $row['subject_name'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Debug the subject class generation
echo "<h2>Subject Class Generation Debug</h2>";
echo "<table border='1'>";
echo "<tr><th>Subject Name</th><th>Generated Class</th><th>Final Class</th></tr>";

// Get all subjects this teacher teaches again for the debug
$stmt = $conn->prepare("
    SELECT ts.subject_id, s.subject_name
    FROM teacher_subjects ts
    JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE ts.teacher_id = ?
    ORDER BY s.subject_name
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $subject_name = $row['subject_name'];
    $subject_class = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $subject_name));
    $valid_subjects = ['english', 'science', 'mathematics', 'history', 'geography'];
    $final_class = in_array($subject_class, $valid_subjects) ? $subject_class : 'default';
    
    echo "<tr>";
    echo "<td>" . $subject_name . "</td>";
    echo "<td>" . $subject_class . "</td>";
    echo "<td>" . $final_class . "</td>";
    echo "</tr>";
}
echo "</table>";
$stmt->close();

$conn->close();
?>

<p><a href="frontend/teacher/manage_fees.php">Go back to Manage Fees</a></p>
