<?php
// Path to php.ini file
$php_ini_path = 'C:/xampp/php/php.ini';

// Check if the file exists
if (!file_exists($php_ini_path)) {
    die("Error: php.ini file not found at $php_ini_path");
}

// Read the file content
$content = file_get_contents($php_ini_path);

// Check if GD is already enabled
if (strpos($content, 'extension=gd') !== false && strpos($content, ';extension=gd') === false) {
    echo "<h2>GD library is already enabled!</h2>";
    echo "<p>No changes were made to php.ini.</p>";
    echo "<p>Please restart Apache server in XAMPP Control Panel.</p>";
    exit;
}

// Replace ;extension=gd with extension=gd
$new_content = str_replace(';extension=gd', 'extension=gd', $content);

// Try to write the file
if (file_put_contents($php_ini_path, $new_content)) {
    echo "<h2>Success!</h2>";
    echo "<p>GD library has been enabled in php.ini.</p>";
    echo "<p>Please restart Apache server in XAMPP Control Panel.</p>";
} else {
    echo "<h2>Error!</h2>";
    echo "<p>Could not write to php.ini. You may need to edit it manually.</p>";
    echo "<h3>Manual Instructions:</h3>";
    echo "<ol>";
    echo "<li>Open C:/xampp/php/php.ini in a text editor (run as administrator)</li>";
    echo "<li>Find the line <code>;extension=gd</code></li>";
    echo "<li>Remove the semicolon (;) so it becomes <code>extension=gd</code></li>";
    echo "<li>Save the file</li>";
    echo "<li>Restart Apache in XAMPP Control Panel</li>";
    echo "</ol>";
}
?>
