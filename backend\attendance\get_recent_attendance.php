<?php
// Include database connection
include_once '../../backend/server/db_connect.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Query to get recent attendance records
$query = "
    SELECT 
        student_id,
        student_name,
        date,
        time,
        grade,
        subject_name,
        week
    FROM 
        attendance
    ORDER BY 
        date DESC, time DESC
    LIMIT 10
";

$result = $conn->query($query);

// Prepare data for response
$records = [];

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $records[] = [
            'student_id' => $row['student_id'],
            'student_name' => $row['student_name'],
            'date' => $row['date'],
            'time' => $row['time'],
            'grade' => $row['grade'],
            'subject_name' => $row['subject_name'],
            'week' => $row['week']
        ];
    }
}

// Return JSON response
echo json_encode($records);

$conn->close();