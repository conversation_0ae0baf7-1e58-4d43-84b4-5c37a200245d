<?php
require_once '../server/db_connect.php';
session_start();

// Debug: Log session data
error_log("Session data in get_student_subjects.php: " . print_r($_SESSION, true));

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['error' => 'Not logged in', 'subjects' => [], 'grade' => '']);
    exit;
}

$user_id = $_SESSION['user_id'];

// Get student's grade and subject information
$studentQuery = "SELECT s.student_id, s.grade
                FROM students s
                WHERE s.user_id = ?";
$studentStmt = $conn->prepare($studentQuery);
$studentStmt->bind_param("i", $user_id);
$studentStmt->execute();
$studentResult = $studentStmt->get_result();

if ($studentResult->num_rows === 0) {
    echo json_encode(['error' => 'No student found', 'subjects' => [], 'grade' => '']);
    exit;
}

$studentData = $studentResult->fetch_assoc();
$grade = $studentData['grade'];
$student_id = $studentData['student_id'];

// Debug: Log student data
error_log("Student data: " . print_r($studentData, true));

// Get all subjects for this student
$subjectsQuery = "SELECT ss.subject_id, s.subject_name
                 FROM student_subjects ss
                 JOIN subjects s ON ss.subject_id = s.subject_id
                 WHERE ss.student_id = ?";
$subjectsStmt = $conn->prepare($subjectsQuery);
$subjectsStmt->bind_param("i", $student_id);
$subjectsStmt->execute();
$subjectsResult = $subjectsStmt->get_result();

$subjects = [];
while ($subjectRow = $subjectsResult->fetch_assoc()) {
    $subjects[] = [
        'subject_id' => $subjectRow['subject_id'],
        'subject_name' => $subjectRow['subject_name']
    ];
}
$subjectsStmt->close();

// Send response
echo json_encode([
    'subjects' => $subjects,
    'grade' => $grade
]);

$studentStmt->close();
$conn->close();
?>
