<?php
session_start();
include_once 'backend/server/db_connect.php';

// Check if user is logged in as teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get teacher_id
$stmt = $conn->prepare("SELECT teacher_id, first_name, last_name FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_id = null;
$teacher_name = "";
if ($row = $result->fetch_assoc()) {
    $teacher_id = $row['teacher_id'];
    $teacher_name = $row['first_name'] . ' ' . $row['last_name'];
}
$stmt->close();

echo "<h1>Raw Teacher Subjects Data</h1>";
echo "<p>Teacher ID: $teacher_id</p>";
echo "<p>Teacher Name: $teacher_name</p>";
echo "<p>User ID: $user_id</p>";

// Get raw data from teacher_subjects table
echo "<h2>Raw Data from teacher_subjects Table</h2>";
$stmt = $conn->prepare("
    SELECT ts.id, ts.teacher_id, ts.subject_id, s.subject_name
    FROM teacher_subjects ts
    JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE ts.teacher_id = ?
    ORDER BY s.subject_name
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();

echo "<table border='1'>";
echo "<tr><th>ID</th><th>Teacher ID</th><th>Subject ID</th><th>Subject Name</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . $row['teacher_id'] . "</td>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "<td>" . $row['subject_name'] . "</td>";
    echo "</tr>";
}
echo "</table>";
$stmt->close();

// Check for duplicate subject IDs
echo "<h2>Check for Duplicate Subject IDs</h2>";
$stmt = $conn->prepare("
    SELECT subject_id, COUNT(*) as count
    FROM teacher_subjects
    WHERE teacher_id = ?
    GROUP BY subject_id
    HAVING COUNT(*) > 1
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    echo "<p>Found duplicate subject IDs:</p>";
    echo "<table border='1'>";
    echo "<tr><th>Subject ID</th><th>Count</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['subject_id'] . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No duplicate subject IDs found.</p>";
}
$stmt->close();

// Get all subjects from the database
echo "<h2>All Subjects in Database</h2>";
$result = $conn->query("SELECT subject_id, subject_name FROM subjects ORDER BY subject_name");
echo "<table border='1'>";
echo "<tr><th>Subject ID</th><th>Subject Name</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "<td>" . $row['subject_name'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Test the query used in manage_fees.php
echo "<h2>Testing Query from manage_fees.php</h2>";
$stmt = $conn->prepare("
    SELECT DISTINCT s.subject_id, s.subject_name
    FROM teacher_subjects ts
    JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE ts.teacher_id = ?
    ORDER BY s.subject_name
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();

echo "<table border='1'>";
echo "<tr><th>Subject ID</th><th>Subject Name</th></tr>";
$teacher_subjects = [];
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "<td>" . $row['subject_name'] . "</td>";
    echo "</tr>";
    
    // Store for later processing
    $teacher_subjects[] = [
        'subject_id' => $row['subject_id'],
        'subject_name' => $row['subject_name']
    ];
}
echo "</table>";
$stmt->close();

// Print the teacher_subjects array
echo "<h2>Teacher Subjects Array</h2>";
echo "<pre>";
print_r($teacher_subjects);
echo "</pre>";

// Add a link to go back to manage_fees.php
echo "<p><a href='frontend/teacher/manage_fees.php'>Go to Manage Fees Page</a></p>";

$conn->close();
?>
