<?php
session_start();
include_once 'server/db_connect.php';
require_once '../libs/tcpdf/tcpdf.php';

// Check if user is logged in and is an owner
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'owner') {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

try {
    // Fetch overall payment data
    $payment_query = "SELECT 
                        SUM(CASE WHEN payment_status = 'PAID' THEN 1 ELSE 0 END) as paid_count,
                        SUM(CASE WHEN payment_status != 'PAID' THEN 1 ELSE 0 END) as unpaid_count,
                        COUNT(*) as total_count
                      FROM students";
    $payment_result = $conn->query($payment_query);
    $payment_data = $payment_result->fetch_assoc();
    
    // Fetch total revenue
    $revenue_query = "SELECT SUM(amount) as total_revenue FROM payment_requests WHERE status = 'PAID'";
    $revenue_result = $conn->query($revenue_query);
    $revenue_data = $revenue_result->fetch_assoc();
    $total_revenue = $revenue_data['total_revenue'] ?? 0;
    
    // Fetch subject-wise payment data
    $subject_query = "SELECT 
                        sub.subject_name,
                        COUNT(s.student_id) as total_students,
                        SUM(CASE WHEN s.payment_status = 'PAID' THEN 1 ELSE 0 END) as paid_students,
                        ROUND((SUM(CASE WHEN s.payment_status = 'PAID' THEN 1 ELSE 0 END) / COUNT(s.student_id)) * 100, 1) as payment_rate
                      FROM 
                        students s
                      JOIN 
                        student_subjects ss ON s.student_id = ss.student_id
                      JOIN 
                        subjects sub ON ss.subject_id = sub.subject_id
                      GROUP BY 
                        sub.subject_id, sub.subject_name
                      ORDER BY 
                        sub.subject_name";
    $subject_result = $conn->query($subject_query);
    
    // Create new PDF document
    $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);
    
    // Set document information
    $pdf->SetCreator('Gurukula Institution');
    $pdf->SetAuthor('Gurukula Institution');
    $pdf->SetTitle('Financial Report');
    $pdf->SetSubject('Financial Report');
    
    // Remove default header/footer
    $pdf->setPrintHeader(false);
    $pdf->setPrintFooter(false);
    
    // Set margins
    $pdf->SetMargins(15, 15, 15);
    
    // Add a page
    $pdf->AddPage();
    
    // Set font
    $pdf->SetFont('helvetica', 'B', 20);
    
    // Title
    $pdf->Cell(0, 15, 'GURUKULA INSTITUTION', 0, 1, 'C');
    $pdf->SetFont('helvetica', 'B', 16);
    $pdf->Cell(0, 10, 'Financial Report', 0, 1, 'C');
    
    // Date
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(0, 10, 'Generated on: ' . date('Y-m-d H:i:s'), 0, 1, 'R');
    
    // Horizontal line
    $pdf->Line(15, $pdf->GetY(), 195, $pdf->GetY());
    $pdf->Ln(5);
    
    // Overall Payment Summary
    $pdf->SetFont('helvetica', 'B', 14);
    $pdf->Cell(0, 10, 'Payment Summary', 0, 1, 'L');
    
    $pdf->SetFont('helvetica', '', 12);
    $pdf->Cell(60, 8, 'Total Students:', 0, 0);
    $pdf->Cell(0, 8, $payment_data['total_count'], 0, 1);
    
    $pdf->Cell(60, 8, 'Paid Students:', 0, 0);
    $pdf->Cell(0, 8, $payment_data['paid_count'], 0, 1);
    
    $pdf->Cell(60, 8, 'Unpaid Students:', 0, 0);
    $pdf->Cell(0, 8, $payment_data['unpaid_count'], 0, 1);
    
    $payment_rate = $payment_data['total_count'] > 0 ? 
        round(($payment_data['paid_count'] / $payment_data['total_count']) * 100, 1) : 0;
    $pdf->Cell(60, 8, 'Overall Payment Rate:', 0, 0);
    $pdf->Cell(0, 8, $payment_rate . '%', 0, 1);
    
    $pdf->Cell(60, 8, 'Total Revenue:', 0, 0);
    $pdf->Cell(0, 8, 'Rs. ' . number_format($total_revenue, 2), 0, 1);
    
    $pdf->Ln(5);
    
    // Subject-wise Payment Data
    $pdf->SetFont('helvetica', 'B', 14);
    $pdf->Cell(0, 10, 'Subject-wise Payment Data', 0, 1, 'L');
    
    // Table header
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->SetFillColor(220, 220, 220);
    $pdf->Cell(70, 8, 'Subject', 1, 0, 'C', true);
    $pdf->Cell(30, 8, 'Total', 1, 0, 'C', true);
    $pdf->Cell(30, 8, 'Paid', 1, 0, 'C', true);
    $pdf->Cell(30, 8, 'Unpaid', 1, 0, 'C', true);
    $pdf->Cell(25, 8, 'Rate (%)', 1, 1, 'C', true);
    
    // Table data
    $pdf->SetFont('helvetica', '', 12);
    $total_subject_students = 0;
    $total_subject_paid = 0;
    
    while ($row = $subject_result->fetch_assoc()) {
        $unpaid = $row['total_students'] - $row['paid_students'];
        
        $pdf->Cell(70, 8, $row['subject_name'], 1, 0, 'L');
        $pdf->Cell(30, 8, $row['total_students'], 1, 0, 'C');
        $pdf->Cell(30, 8, $row['paid_students'], 1, 0, 'C');
        $pdf->Cell(30, 8, $unpaid, 1, 0, 'C');
        $pdf->Cell(25, 8, $row['payment_rate'] . '%', 1, 1, 'C');
        
        $total_subject_students += $row['total_students'];
        $total_subject_paid += $row['paid_students'];
    }
    
    // Table footer (totals)
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->SetFillColor(240, 240, 240);
    $total_subject_unpaid = $total_subject_students - $total_subject_paid;
    $total_subject_rate = $total_subject_students > 0 ? 
        round(($total_subject_paid / $total_subject_students) * 100, 1) : 0;
    
    $pdf->Cell(70, 8, 'TOTAL', 1, 0, 'L', true);
    $pdf->Cell(30, 8, $total_subject_students, 1, 0, 'C', true);
    $pdf->Cell(30, 8, $total_subject_paid, 1, 0, 'C', true);
    $pdf->Cell(30, 8, $total_subject_unpaid, 1, 0, 'C', true);
    $pdf->Cell(25, 8, $total_subject_rate . '%', 1, 1, 'C', true);
    
    // Footer note
    $pdf->Ln(10);
    $pdf->SetFont('helvetica', 'I', 10);
    $pdf->Cell(0, 5, 'Note: This is an automatically generated report. For any queries, please contact the administrator.', 0, 1, 'L');
    
    // Output the PDF
    $pdf->Output('Financial_Report_' . date('Y-m-d') . '.pdf', 'D');
    
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode(['error' => $e->getMessage()]);
    exit();
}
?>