<?php
session_start();
include('../../backend/server/db_connect.php');
include_once '../../assets/teacher_sidebar.php';
?>

<!DOCTYPE html>
<html>
<head>
  <title>Teacher Settings</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-bg: #f4f6f9;
      --dark-text: #2d3748;
      --light-text: #718096;
      --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      --border-radius: 12px;
      --transition-speed: 0.3s;
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: var(--light-bg);
      color: var(--dark-text);
    }

    .settings-container {
      margin-left: 290px;
      padding: 30px;
    }

    .page-header {
      margin-bottom: 30px;

    }

    .page-header h1 {
      font-weight: 600;
      color: var(--dark-text);
      margin-bottom: 10px;

    }

    .settings-wrapper {
      display: flex;
      gap: 30px;
    }

    .settings-menu {
      flex: 0 0 300px;
    }

    .settings-content {
      flex: 1;
    }

    .nav-pills .nav-link {
      color: var(--dark-text);
      border-radius: var(--border-radius);
      padding: 12px 20px;
      margin-bottom: 10px;
      transition: all var(--transition-speed) ease;
      display: flex;
      align-items: center;
      font-weight: 500;
    }

    .nav-pills .nav-link i {
      margin-right: 12px;
      font-size: 1.1rem;
    }

    .nav-pills .nav-link.active {
      background-color: var(--primary-color);
      color: white;
      box-shadow: var(--card-shadow);
    }

    .nav-pills .nav-link:hover:not(.active) {
      background-color: rgba(67, 97, 238, 0.1);
      transform: translateX(5px);
    }

    .card {
      border: none;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
      transition: all var(--transition-speed) ease;
      overflow: hidden;
    }

    .card:hover {
      box-shadow: var(--hover-shadow);
    }

    .card-header {
      background-color: white;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      padding: 20px;
      font-weight: 600;
    }

    .card-body {
      padding: 25px;
    }

    .form-label {
      font-weight: 500;
      color: var(--dark-text);
      margin-bottom: 8px;
    }

    .form-control {
      border-radius: 8px;
      padding: 12px 15px;
      border: 1px solid #e2e8f0;
      transition: all 0.2s ease;
    }

    .form-control:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
    }

    .btn-primary {
      background-color: var(--primary-color);
      border: none;
      border-radius: 8px;
      padding: 12px 25px;
      font-weight: 500;
      transition: all 0.2s ease;
    }

    .btn-primary:hover {
      background-color: var(--secondary-color);
      transform: translateY(-2px);
    }

    .list-group-item {
      border: none;
      padding: 15px;
      border-radius: 8px !important;
      margin-bottom: 8px;
      background-color: #f8fafc;
    }

    @media (max-width: 992px) {
      .settings-wrapper {
        flex-direction: column;
      }

      .settings-menu {
        flex: 0 0 100%;
      }
    }

    @media (max-width: 768px) {
      .settings-container {
        margin-left: 0;
        padding: 15px;
      }
    }
  </style>
</head>
<body>

<div class="settings-container">
  <!-- Update the page header to be a card with gradient styling -->
  <div class="card border-0 shadow-sm rounded-4 mb-4">
    <div class="card-body p-4" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); color: white;">
      <h1 class="fw-bold mb-2">Account Settings</h1>
      <p class="mb-0" style="opacity: 0.9;">Manage your account preferences and information</p>
    </div>
  </div>

  <div class="settings-wrapper">
    <div class="settings-menu">
      <div class="card">
        <div class="card-body p-0">
          <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
            <button class="nav-link active" id="profile-tab" data-bs-toggle="pill" data-bs-target="#profile" type="button" role="tab">
              <i class="fas fa-user-circle"></i> Profile Information
            </button>
            <button class="nav-link" id="password-tab" data-bs-toggle="pill" data-bs-target="#password" type="button" role="tab">
              <i class="fas fa-lock"></i> Change Password
            </button>
            <button class="nav-link" id="notifications-tab" data-bs-toggle="pill" data-bs-target="#notifications" type="button" role="tab">
              <i class="fas fa-bell"></i> Notification Preferences
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="settings-content">
      <div class="tab-content" id="v-pills-tabContent">
        <!-- Profile Form -->
        <div class="tab-pane fade show active" id="profile" role="tabpanel" aria-labelledby="profile-tab">
          <div class="card">
            <div class="card-header d-flex align-items-center">
              <i class="fas fa-user-circle me-2 text-primary"></i>
              Profile Information
            </div>
            <div class="card-body">
              <?php
              // Get teacher profile information
              $profile_query = $conn->prepare("SELECT first_name, last_name, email FROM users WHERE id = ?");
              $profile_query->bind_param("i", $_SESSION['user_id']);
              $profile_query->execute();
              $profile_result = $profile_query->get_result();
              $profile = $profile_result->fetch_assoc();

              // Set default values if no profile found
              $first_name = $profile['first_name'] ?? '';
              $last_name = $profile['last_name'] ?? '';
              $email = $profile['email'] ?? '';
              ?>
              <form action="../../backend/settings/update_profile.php" method="POST">
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="first_name" class="form-label">First Name</label>
                    <input type="text" class="form-control" name="first_name" id="first_name" value="<?php echo htmlspecialchars($first_name); ?>" required>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label for="last_name" class="form-label">Last Name</label>
                    <input type="text" class="form-control" name="last_name" id="last_name" value="<?php echo htmlspecialchars($last_name); ?>" required>
                  </div>
                </div>

                <div class="mb-3">
                  <label for="email" class="form-label">Email Address</label>
                  <input type="email" class="form-control" name="email" id="email" value="<?php echo htmlspecialchars($email); ?>" required>
                </div>

                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-save me-2"></i> Update Profile
                </button>
              </form>
            </div>
          </div>
        </div>

        <!-- Change Password Form -->
        <div class="tab-pane fade" id="password" role="tabpanel" aria-labelledby="password-tab">
          <div class="card">
            <div class="card-header d-flex align-items-center">
              <i class="fas fa-lock me-2 text-primary"></i>
              Change Password
            </div>
            <div class="card-body">
              <form action="../../backend/settings/change_password.php" method="POST" id="passwordForm">
                <div class="mb-3 position-relative">
                  <label for="current_password" class="form-label">Current Password</label>
                  <div class="input-group">
                    <input type="password" class="form-control" name="current_password" id="current_password" required>
                    <button class="btn btn-outline-secondary password-toggle" type="button" tabindex="-1">
                      <i class="fas fa-eye"></i>
                    </button>
                  </div>
                </div>

                <div class="mb-3 position-relative">
                  <label for="new_password" class="form-label">New Password</label>
                  <div class="input-group">
                    <input type="password" class="form-control" name="new_password" id="new_password" required>
                    <button class="btn btn-outline-secondary password-toggle" type="button" tabindex="-1">
                      <i class="fas fa-eye"></i>
                    </button>
                  </div>
                  <div class="password-strength mt-2 d-none">
                    <div class="progress" style="height: 5px;">
                      <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted strength-text mt-1 d-inline-block"></small>
                  </div>
                </div>

                <div class="mb-4 position-relative">
                  <label for="confirm_password" class="form-label">Confirm New Password</label>
                  <div class="input-group">
                    <input type="password" class="form-control" name="confirm_password" id="confirm_password" required>
                    <button class="btn btn-outline-secondary password-toggle" type="button" tabindex="-1">
                      <i class="fas fa-eye"></i>
                    </button>
                  </div>
                  <div id="passwordMatch" class="form-text mt-2"></div>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                  <button type="submit" class="btn btn-primary">
                    <i class="fas fa-key me-2"></i> Change Password
                  </button>
                  <div id="passwordFeedback" class="text-end"></div>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Notification Preferences -->
        <div class="tab-pane fade" id="notifications" role="tabpanel" aria-labelledby="notifications-tab">
          <div class="card">
            <div class="card-header d-flex align-items-center">
              <i class="fas fa-bell me-2 text-primary"></i>
              Notification Preferences
            </div>
            <div class="card-body">
              <?php
              // Get teacher notification preferences
              $notification_query = $conn->prepare("SELECT email_notifications, popup_notifications FROM teachers WHERE user_id = ?");
              $notification_query->bind_param("i", $_SESSION['user_id']);
              $notification_query->execute();
              $notification_result = $notification_query->get_result();
              $notification_prefs = $notification_result->fetch_assoc();

              // Set default values if no preferences found
              $email_notifications = isset($notification_prefs['email_notifications']) ? $notification_prefs['email_notifications'] : 0;
              $popup_notifications = isset($notification_prefs['popup_notifications']) ? $notification_prefs['popup_notifications'] : 0;
              ?>

              <form action="../../backend/settings/update_notifications.php" method="POST">
                <div class="mb-4">
                  <h5 class="mb-3">Class Reminders</h5>


                  <div class="form-check form-switch mb-3">
                    <input type="checkbox" class="form-check-input" id="popup_notifications" name="popup_notifications"
                      <?php echo ($popup_notifications == 1) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="popup_notifications">
                      <i class="fas fa-bell me-2 text-primary"></i> Enable Class Notifications
                    </label>
                    <div class="form-text">Receive popup notifications about upcoming classes when you're logged in</div>
                  </div>
                </div>

                <div class="mb-4">
                  <h5 class="mb-3">Reminder Timing</h5>
                  <div class="card bg-light border-0">
                    <div class="card-body">
                      <ul class="list-group list-group-flush bg-transparent">
                        <li class="list-group-item bg-transparent px-0">
                          <i class="fas fa-clock me-2 text-info"></i> 10 minutes before each class
                        </li>
                        <li class="list-group-item bg-transparent px-0">
                          <i class="fas fa-calendar-day me-2 text-success"></i> One day before each class
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-save me-2"></i> Save Preferences
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tabs
    const tabEls = document.querySelectorAll('button[data-bs-toggle="pill"]');
    tabEls.forEach(tabEl => {
      tabEl.addEventListener('click', function(event) {
        event.preventDefault();
        const bsTab = new bootstrap.Tab(tabEl);
        bsTab.show();
      });
    });

    // Password visibility toggle
    const toggleButtons = document.querySelectorAll('.password-toggle');
    toggleButtons.forEach(button => {
      button.addEventListener('click', function() {
        const input = this.previousElementSibling;
        const icon = this.querySelector('i');

        if (input.type === 'password') {
          input.type = 'text';
          icon.classList.remove('fa-eye');
          icon.classList.add('fa-eye-slash');
        } else {
          input.type = 'password';
          icon.classList.remove('fa-eye-slash');
          icon.classList.add('fa-eye');
        }
      });
    });

    // Password strength meter
    const newPassword = document.getElementById('new_password');
    const strengthBar = document.querySelector('.progress-bar');
    const strengthText = document.querySelector('.strength-text');
    const strengthContainer = document.querySelector('.password-strength');

    if (newPassword) {
      newPassword.addEventListener('input', function() {
        const password = this.value;
        let strength = 0;
        let feedback = '';

        if (password.length > 0) {
          strengthContainer.classList.remove('d-none');

          // Check password length
          if (password.length >= 8) strength += 25;

          // Check for mixed case
          if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength += 25;

          // Check for numbers
          if (password.match(/\d/)) strength += 25;

          // Check for special characters
          if (password.match(/[^a-zA-Z\d]/)) strength += 25;

          // Update the strength bar
          strengthBar.style.width = strength + '%';

          // Update color based on strength
          if (strength < 25) {
            strengthBar.className = 'progress-bar bg-danger';
            feedback = 'Very Weak';
          } else if (strength < 50) {
            strengthBar.className = 'progress-bar bg-warning';
            feedback = 'Weak';
          } else if (strength < 75) {
            strengthBar.className = 'progress-bar bg-info';
            feedback = 'Medium';
          } else {
            strengthBar.className = 'progress-bar bg-success';
            feedback = 'Strong';
          }

          strengthText.textContent = feedback;
        } else {
          strengthContainer.classList.add('d-none');
        }
      });
    }

    // Password match validation
    const confirmPassword = document.getElementById('confirm_password');
    const passwordMatch = document.getElementById('passwordMatch');

    if (confirmPassword && newPassword) {
      function checkPasswordMatch() {
        if (confirmPassword.value.length > 0) {
          if (newPassword.value === confirmPassword.value) {
            passwordMatch.innerHTML = '<span class="text-success"><i class="fas fa-check-circle me-1"></i>Passwords match</span>';
            confirmPassword.classList.remove('is-invalid');
            confirmPassword.classList.add('is-valid');
          } else {
            passwordMatch.innerHTML = '<span class="text-danger"><i class="fas fa-times-circle me-1"></i>Passwords do not match</span>';
            confirmPassword.classList.remove('is-valid');
            confirmPassword.classList.add('is-invalid');
          }
        } else {
          passwordMatch.innerHTML = '';
          confirmPassword.classList.remove('is-valid', 'is-invalid');
        }
      }

      newPassword.addEventListener('input', checkPasswordMatch);
      confirmPassword.addEventListener('input', checkPasswordMatch);
    }

    // Form submission validation
    const passwordForm = document.getElementById('passwordForm');
    const passwordFeedback = document.getElementById('passwordFeedback');

    if (passwordForm) {
      passwordForm.addEventListener('submit', function(e) {
        if (newPassword.value !== confirmPassword.value) {
          e.preventDefault();
          passwordFeedback.innerHTML = '<span class="text-danger"><i class="fas fa-exclamation-circle me-1"></i>Passwords must match</span>';
          return false;
        }

        if (newPassword.value.length < 8) {
          e.preventDefault();
          passwordFeedback.innerHTML = '<span class="text-danger"><i class="fas fa-exclamation-circle me-1"></i>Password must be at least 8 characters</span>';
          return false;
        }

        return true;
      });
    }
  });
</script>
</body>
</html>
