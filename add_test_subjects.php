<?php
include_once 'backend/server/db_connect.php';

echo "<h1>Add Test Subjects</h1>";

// Get all subjects
$result = $conn->query("SELECT subject_id, subject_name FROM subjects ORDER BY subject_name");
$subjects = [];
while ($row = $result->fetch_assoc()) {
    $subjects[$row['subject_id']] = $row['subject_name'];
}

echo "<h2>Available Subjects</h2>";
echo "<table border='1'>";
echo "<tr><th>Subject ID</th><th>Subject Name</th></tr>";
foreach ($subjects as $id => $name) {
    echo "<tr>";
    echo "<td>" . $id . "</td>";
    echo "<td>" . $name . "</td>";
    echo "</tr>";
}
echo "</table>";

// Get all teachers
$result = $conn->query("SELECT t.teacher_id, t.first_name, t.last_name, u.email FROM teachers t JOIN users u ON t.user_id = u.id ORDER BY t.teacher_id");
$teachers = [];
while ($row = $result->fetch_assoc()) {
    $teachers[$row['teacher_id']] = $row['first_name'] . ' ' . $row['last_name'] . ' (' . $row['email'] . ')';
}

echo "<h2>Available Teachers</h2>";
echo "<table border='1'>";
echo "<tr><th>Teacher ID</th><th>Teacher Name</th></tr>";
foreach ($teachers as $id => $name) {
    echo "<tr>";
    echo "<td>" . $id . "</td>";
    echo "<td>" . $name . "</td>";
    echo "</tr>";
}
echo "</table>";

// Form to add a subject to a teacher
echo "<h2>Add Subject to Teacher</h2>";
echo "<form method='post'>";
echo "<label for='teacher_id'>Select Teacher:</label>";
echo "<select name='teacher_id' id='teacher_id'>";
foreach ($teachers as $id => $name) {
    echo "<option value='" . $id . "'>" . $name . "</option>";
}
echo "</select><br><br>";

echo "<label for='subject_id'>Select Subject:</label>";
echo "<select name='subject_id' id='subject_id'>";
foreach ($subjects as $id => $name) {
    echo "<option value='" . $id . "'>" . $name . "</option>";
}
echo "</select><br><br>";

echo "<input type='submit' name='add_subject' value='Add Subject to Teacher'>";
echo "</form>";

// Handle form submission
if (isset($_POST['add_subject'])) {
    $teacher_id = $_POST['teacher_id'];
    $subject_id = $_POST['subject_id'];
    
    // Check if this combination already exists
    $stmt = $conn->prepare("SELECT id FROM teacher_subjects WHERE teacher_id = ? AND subject_id = ?");
    $stmt->bind_param("ii", $teacher_id, $subject_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<div style='color: red; margin-top: 20px;'>This teacher is already assigned to this subject!</div>";
    } else {
        // Add the subject to the teacher
        $stmt = $conn->prepare("INSERT INTO teacher_subjects (teacher_id, subject_id) VALUES (?, ?)");
        $stmt->bind_param("ii", $teacher_id, $subject_id);
        
        if ($stmt->execute()) {
            echo "<div style='color: green; margin-top: 20px;'>Subject successfully added to teacher!</div>";
        } else {
            echo "<div style='color: red; margin-top: 20px;'>Error adding subject: " . $stmt->error . "</div>";
        }
    }
    $stmt->close();
}

// Show current teacher-subject assignments
echo "<h2>Current Teacher-Subject Assignments</h2>";
$result = $conn->query("
    SELECT ts.id, ts.teacher_id, t.first_name, t.last_name, ts.subject_id, s.subject_name
    FROM teacher_subjects ts
    JOIN teachers t ON ts.teacher_id = t.teacher_id
    JOIN subjects s ON ts.subject_id = s.subject_id
    ORDER BY t.teacher_id, s.subject_name
");

echo "<table border='1'>";
echo "<tr><th>ID</th><th>Teacher ID</th><th>Teacher Name</th><th>Subject ID</th><th>Subject Name</th><th>Action</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . $row['teacher_id'] . "</td>";
    echo "<td>" . $row['first_name'] . ' ' . $row['last_name'] . "</td>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "<td>" . $row['subject_name'] . "</td>";
    echo "<td><a href='?delete=" . $row['id'] . "' onclick='return confirm(\"Are you sure?\")'>Delete</a></td>";
    echo "</tr>";
}
echo "</table>";

// Handle deletion
if (isset($_GET['delete'])) {
    $id = (int)$_GET['delete'];
    $stmt = $conn->prepare("DELETE FROM teacher_subjects WHERE id = ?");
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        echo "<div style='color: green; margin-top: 20px;'>Assignment deleted successfully!</div>";
        echo "<script>window.location.href = 'add_test_subjects.php';</script>";
    } else {
        echo "<div style='color: red; margin-top: 20px;'>Error deleting assignment: " . $stmt->error . "</div>";
    }
    $stmt->close();
}

// Add links to navigate
echo "<p><a href='frontend/teacher/manage_fees.php'>Go to Manage Fees Page</a></p>";

$conn->close();
?>
