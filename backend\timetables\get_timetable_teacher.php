<?php
session_start();
header('Content-Type: application/json');
include_once '../../backend/server/db_connect.php';

// Get teacher_id from session or use default for testing
$user_id = $_SESSION['user_id'] ?? 1;

// First get the teacher_id
$teacherStmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$teacherStmt->bind_param("i", $user_id);
$teacherStmt->execute();
$teacherResult = $teacherStmt->get_result();
$teacherData = $teacherResult->fetch_assoc();
$teacher_id = $teacherData['teacher_id'] ?? 1;
$teacherStmt->close();

// Now get the timetable
$result = $conn->query("
  SELECT s.subject_name, tt.grade, tt.day_of_week, tt.start_time, tt.end_time 
  FROM teacher_timetable tt
  JOIN subjects s ON tt.subject_id = s.subject_id
  WHERE tt.teacher_id = $teacher_id
  ORDER BY FIELD(tt.day_of_week, 'Monday','Tuesday','Wednesday','Thursday','Friday','Saturday','Sunday'), tt.start_time
");


$data = [];
while ($row = $result->fetch_assoc()) {
  $data[] = [
    'subject' => $row['subject_name'],
    'grade' => $row['grade'],
    'day' => $row['day_of_week'],
    'start_time' => $row['start_time'],
    'end_time' => $row['end_time'],
  ];
}
echo json_encode($data);
?>
