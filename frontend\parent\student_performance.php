<?php
session_start();
include_once '../../backend/server/db_connect.php';

// Check if user is logged in as parent
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'parent') {
    header("Location: ../../backend/server/login.php");
    exit();
}

// Get parent's child student ID
$parent_id = $_SESSION['user_id'] ?? 0;
$stmt = $conn->prepare("SELECT child_student_id FROM parents WHERE user_id = ?");
$stmt->bind_param("i", $parent_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $student_id = $row['child_student_id'];

    // Get student name
    $stmt = $conn->prepare("SELECT CONCAT(u.first_name, ' ', u.last_name) as student_name, s.grade
                           FROM students s
                           JOIN users u ON s.user_id = u.id
                           WHERE s.student_id = ?");
    $stmt->bind_param("i", $student_id);
    $stmt->execute();
    $student_result = $stmt->get_result();
    $student_info = $student_result->fetch_assoc();
} else {
    $student_id = 0;
    $student_info = null;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Performance - Gurukula Institution</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --success-color: #1cc88a;
            --info-color: #36b9cc;
            --warning-color: #f6c23e;
            --danger-color: #e74a3b;
            --light-color: #f4f6f9;
            --dark-color: #5a5c69;
            --card-shadow: 0 0.15rem 1.75rem rgba(0, 0, 0, 0.1);
            --hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.15);
        }

        body {
            background-color: var(--light-color);
            font-family: 'Nunito', sans-serif;
            color: var(--dark-color);
        }

        .content {
            margin-left: 280px;
            padding: 30px;
            transition: all 0.3s ease;
        }

        /* Page header */
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--card-shadow);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 200%;
            background: rgba(255, 255, 255, 0.1);
            transform: rotate(30deg);
        }

        .page-header h2 {
            color: white;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
        }

        .page-header p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 0;
            position: relative;
            z-index: 1;
        }

        /* Student info card */
        .student-info-card {
            background-color: white;
            border-radius: 0.75rem;
            box-shadow: var(--card-shadow);
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            border: none;
        }

        .student-info-card:hover {
            box-shadow: var(--hover-shadow);
        }

        .student-info-card .card-header {
            background-color: var(--info-color);
            color: white;
            border-radius: 0.75rem 0.75rem 0 0;
            padding: 1rem 1.5rem;
            font-weight: 600;
            border: none;
        }

        .student-info-card .card-body {
            padding: 1.5rem;
        }

        /* Performance summary */
        .performance-summary {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            margin-bottom: 20px;
            gap: 15px;
        }

        .summary-item {
            text-align: center;
            padding: 1.5rem;
            background-color: #fff;
            border-radius: 0.75rem;
            box-shadow: var(--card-shadow);
            flex: 1;
            min-width: 200px;
            transition: all 0.3s ease;
            border: none;
        }

        .summary-item:hover {
            box-shadow: var(--hover-shadow);
            transform: translateY(-5px);
        }

        .summary-item h3 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .summary-item p {
            margin: 10px 0 0;
            color: var(--dark-color);
            font-weight: 600;
            font-size: 1rem;
        }

        .summary-item i {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        /* Chart cards */
        .chart-card {
            background-color: white;
            border-radius: 0.75rem;
            box-shadow: var(--card-shadow);
            margin-bottom: 25px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: none;
        }

        .chart-card:hover {
            box-shadow: var(--hover-shadow);
        }

        .chart-card .card-header {
            padding: 1rem 1.5rem;
            font-weight: 600;
            border: none;
            display: flex;
            align-items: center;
        }

        .chart-card .card-header i {
            margin-right: 10px;
        }

        .chart-card .card-header h5 {
            margin: 0;
            font-weight: 700;
        }

        .chart-card .card-body {
            padding: 1.5rem;
        }

        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        /* Color classes */
        .excellent {
            color: var(--success-color);
        }

        .good {
            color: var(--info-color);
        }

        .average {
            color: var(--warning-color);
        }

        .poor {
            color: var(--danger-color);
        }

        /* Alert styling */
        .alert {
            border-radius: 0.75rem;
            padding: 1.25rem;
            margin-bottom: 1.5rem;
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .alert-info {
            background-color: rgba(54, 185, 204, 0.1);
            color: var(--info-color);
            border-left: 4px solid var(--info-color);
        }

        .alert-warning {
            background-color: rgba(246, 194, 62, 0.1);
            color: var(--warning-color);
            border-left: 4px solid var(--warning-color);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .content {
                margin-left: 0;
                padding: 15px;
            }

            .performance-summary {
                flex-direction: column;
            }

            .summary-item {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <?php
    // Set current page for sidebar highlighting
    $currentPage = basename($_SERVER['PHP_SELF']);
    include_once '../../assets/parent_sidebar.php';
    ?>

    <div class="content">
        <div class="page-header">
            <h2><i class="fas fa-chart-line me-2"></i>Student Performance</h2>
            <p>Track your child's academic progress and performance metrics</p>
        </div>

        <?php if ($student_info): ?>
            <div class="student-info-card">
                <div class="card-header">
                    <i class="fas fa-user-graduate me-2"></i>Student Information
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong><i class="fas fa-user me-2"></i>Name:</strong> <?= htmlspecialchars($student_info['student_name']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong><i class="fas fa-graduation-cap me-2"></i>Grade:</strong> <?= htmlspecialchars($student_info['grade']) ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Summary -->
            <div class="performance-summary">
                <div class="summary-item">
                    <i class="fas fa-calendar-check"></i>
                    <h3 id="avgAttendance">-</h3>
                    <p>Average Attendance</p>
                </div>
                <div class="summary-item">
                    <i class="fas fa-award"></i>
                    <h3 id="avgMarks">-</h3>
                    <p>Average Marks</p>
                </div>
                <div class="summary-item">
                    <i class="fas fa-book"></i>
                    <h3 id="totalSubjects">-</h3>
                    <p>Total Subjects</p>
                </div>
            </div>

            <!-- Attendance Chart -->
            <div class="chart-card">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-calendar-alt"></i>
                    <h5>Attendance by Subject</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="attendanceChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Marks Chart -->
            <div class="chart-card">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-chart-bar"></i>
                    <h5>Marks by Subject</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="marksChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Progress Over Time -->
            <div class="chart-card">
                <div class="card-header bg-info text-white">
                    <i class="fas fa-chart-line"></i>
                    <h5>Progress Over Time</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="progressChart"></canvas>
                    </div>
                </div>
            </div>

        <?php else: ?>
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>No Student Information Found</h5>
                <p>We couldn't find any student associated with your account. Please contact the administrator.</p>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <?php if ($student_info): ?>
    <script>
        // Function to animate value changes
        function animateValue(id, start, end, duration, suffix = '') {
            if (start === end) return;
            const range = end - start;
            let current = start;
            const increment = end > start ? 1 : -1;
            const stepTime = Math.abs(Math.floor(duration / range));
            const obj = document.getElementById(id);

            const timer = setInterval(function() {
                current += increment;
                obj.innerHTML = current + suffix;
                if (current == end) {
                    clearInterval(timer);
                }
            }, stepTime);
        }
        $(document).ready(function() {
            const studentId = <?= $student_id ?>;

            // Fetch performance data
            $.ajax({
                url: '../../backend/parent/get_student_performance.php',
                type: 'GET',
                data: { student_id: studentId },
                dataType: 'json',
                success: function(data) {
                    console.log("Performance data received:", data);

                    // Check if summary data exists
                    if (!data.summary) {
                        console.error("Summary data is missing in the response");
                        return;
                    }

                    // Update summary metrics with animation
                    animateValue("avgAttendance", 0, data.summary.avgAttendance || 0, 1500, "%");
                    animateValue("avgMarks", 0, data.summary.avgMarks || 0, 1500, "");
                    animateValue("totalSubjects", 0, data.summary.totalSubjects || 0, 1000, "");

                    // Set color for average marks based on performance
                    const avgMarks = data.summary.avgMarks;
                    if (avgMarks >= 85) {
                        $('#avgMarks').addClass('excellent');
                    } else if (avgMarks >= 70) {
                        $('#avgMarks').addClass('good');
                    } else if (avgMarks >= 50) {
                        $('#avgMarks').addClass('average');
                    } else {
                        $('#avgMarks').addClass('poor');
                    }

                    // Create attendance chart
                    const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
                    new Chart(attendanceCtx, {
                        type: 'bar',
                        data: {
                            labels: data.attendance.subjects,
                            datasets: [{
                                label: 'Attendance (%)',
                                data: data.attendance.percentages,
                                backgroundColor: data.attendance.subjects.map((_, i) => {
                                    const value = data.attendance.percentages[i];
                                    if (value >= 90) return 'rgba(40, 167, 69, 0.7)';
                                    if (value >= 75) return 'rgba(23, 162, 184, 0.7)';
                                    if (value >= 60) return 'rgba(255, 193, 7, 0.7)';
                                    return 'rgba(220, 53, 69, 0.7)';
                                }),
                                borderWidth: 1,
                                borderRadius: 6
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100,
                                    grid: {
                                        display: true,
                                        color: 'rgba(0, 0, 0, 0.05)'
                                    },
                                    ticks: {
                                        font: {
                                            family: "'Nunito', sans-serif"
                                        }
                                    },
                                    title: {
                                        display: true,
                                        text: 'Attendance (%)',
                                        font: {
                                            family: "'Nunito', sans-serif",
                                            weight: 'bold'
                                        }
                                    }
                                },
                                x: {
                                    grid: {
                                        display: false
                                    },
                                    ticks: {
                                        font: {
                                            family: "'Nunito', sans-serif"
                                        }
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    labels: {
                                        font: {
                                            family: "'Nunito', sans-serif"
                                        }
                                    }
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                    padding: 10,
                                    titleFont: {
                                        family: "'Nunito', sans-serif",
                                        size: 14
                                    },
                                    bodyFont: {
                                        family: "'Nunito', sans-serif",
                                        size: 13
                                    },
                                    cornerRadius: 6
                                }
                            }
                        }
                    });

                    // Create marks chart
                    const marksCtx = document.getElementById('marksChart').getContext('2d');
                    new Chart(marksCtx, {
                        type: 'bar',
                        data: {
                            labels: data.marks.subjects,
                            datasets: [{
                                label: 'Marks',
                                data: data.marks.values,
                                backgroundColor: data.marks.subjects.map((_, i) => {
                                    const value = data.marks.values[i];
                                    if (value >= 85) return 'rgba(40, 167, 69, 0.7)';
                                    if (value >= 70) return 'rgba(23, 162, 184, 0.7)';
                                    if (value >= 50) return 'rgba(255, 193, 7, 0.7)';
                                    return 'rgba(220, 53, 69, 0.7)';
                                }),
                                borderWidth: 1,
                                borderRadius: 6
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100,
                                    grid: {
                                        display: true,
                                        color: 'rgba(0, 0, 0, 0.05)'
                                    },
                                    ticks: {
                                        font: {
                                            family: "'Nunito', sans-serif"
                                        }
                                    },
                                    title: {
                                        display: true,
                                        text: 'Marks',
                                        font: {
                                            family: "'Nunito', sans-serif",
                                            weight: 'bold'
                                        }
                                    }
                                },
                                x: {
                                    grid: {
                                        display: false
                                    },
                                    ticks: {
                                        font: {
                                            family: "'Nunito', sans-serif"
                                        }
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    labels: {
                                        font: {
                                            family: "'Nunito', sans-serif"
                                        }
                                    }
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                    padding: 10,
                                    titleFont: {
                                        family: "'Nunito', sans-serif",
                                        size: 14
                                    },
                                    bodyFont: {
                                        family: "'Nunito', sans-serif",
                                        size: 13
                                    },
                                    cornerRadius: 6
                                }
                            }
                        }
                    });

                    // Create progress chart
                    const progressCtx = document.getElementById('progressChart').getContext('2d');
                    new Chart(progressCtx, {
                        type: 'line',
                        data: {
                            labels: data.progress.months,
                            datasets: [{
                                label: 'Average Marks',
                                data: data.progress.marks,
                                borderColor: 'rgba(40, 167, 69, 1)',
                                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                                tension: 0.3,
                                fill: true,
                                borderWidth: 3,
                                pointBackgroundColor: 'rgba(40, 167, 69, 1)',
                                pointBorderColor: '#fff',
                                pointRadius: 5,
                                pointHoverRadius: 7
                            }, {
                                label: 'Attendance (%)',
                                data: data.progress.attendance,
                                borderColor: 'rgba(0, 123, 255, 1)',
                                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                                tension: 0.3,
                                fill: true
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: 'Value'
                                    }
                                }
                            },
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'Progress Over Last 6 Months'
                                }
                            }
                        }
                    });
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching performance data:', error);
                    alert('Failed to load performance data. Please try again later.');
                }
            });
        });
    </script>
    <?php endif; ?>
</body>
</html>
