<?php
include_once 'backend/server/db_connect.php';

echo "<h1>Subject Fees Debug Information</h1>";

// Check if subject_fees table exists
$result = $conn->query("SHOW TABLES LIKE 'subject_fees'");
if ($result->num_rows == 0) {
    echo "<p>The subject_fees table does not exist!</p>";
    exit();
}

// Get structure of subject_fees table
$result = $conn->query("DESCRIBE subject_fees");
echo "<h2>Subject Fees Table Structure</h2>";
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "<td>" . $row['Extra'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Get all records from subject_fees table
$result = $conn->query("SELECT * FROM subject_fees ORDER BY subject_id, grade");
echo "<h2>All Subject Fees Records</h2>";
if ($result->num_rows == 0) {
    echo "<p>No records found in the subject_fees table.</p>";
} else {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Subject ID</th><th>Grade</th><th>Fee</th><th>Created At</th><th>Updated At</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['subject_id'] . "</td>";
        echo "<td>" . $row['grade'] . "</td>";
        echo "<td>" . $row['fee'] . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "<td>" . $row['updated_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Get subject names for each subject_id in subject_fees
$result = $conn->query("
    SELECT sf.subject_id, s.subject_name, COUNT(*) as fee_count
    FROM subject_fees sf
    JOIN subjects s ON sf.subject_id = s.subject_id
    GROUP BY sf.subject_id, s.subject_name
    ORDER BY s.subject_name
");

echo "<h2>Subject Fee Counts by Subject</h2>";
if ($result->num_rows == 0) {
    echo "<p>No records found.</p>";
} else {
    echo "<table border='1'>";
    echo "<tr><th>Subject ID</th><th>Subject Name</th><th>Fee Count</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['subject_id'] . "</td>";
        echo "<td>" . $row['subject_name'] . "</td>";
        echo "<td>" . $row['fee_count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

$conn->close();
?>
