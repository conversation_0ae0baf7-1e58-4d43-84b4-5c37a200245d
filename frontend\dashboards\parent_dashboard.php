<?php
session_start();
include_once '../../backend/server/db_connect.php';

// Check if user is logged in as parent
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'parent') {
    header("Location: ../../backend/server/login.php");
    exit();
}

// Ensure session variables are properly set for auth_check.php
$_SESSION['user_id'] = $_SESSION['user_id'];
$_SESSION['role'] = 'parent';
?>
<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
  <meta charset="UTF-8">
  <title>Parent Dashboard - Gurukula Institution</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome for icons -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --success-color: #1cc88a;
      --info-color: #36b9cc;
      --warning-color: #f6c23e;
      --danger-color: #e74a3b;
      --light-color: #f8f9fc;
      --dark-color: #5a5c69;
      --card-shadow: 0 0.15rem 1.75rem rgba(0, 0, 0, 0.1);
      --hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.15);
    }

    body {
      background-color: var(--light-color);
      font-family: 'Nunito', sans-serif;
      font-size: 1rem;
      color: var(--dark-color);
    }

    /* Main content styles */
    .content {
      margin-left: 280px;
      padding: 30px;
      transition: all 0.3s ease;
    }

    /* Welcome header */
    .welcome-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
    }

    .welcome-header::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 200%;
      background: rgba(255, 255, 255, 0.1);
      transform: rotate(30deg);
    }

    .welcome-header h2 {
      color: white;
      font-weight: 700;
      margin-bottom: 0.5rem;
      position: relative;
      z-index: 1;
    }

    .welcome-header p {
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 0;
      position: relative;
      z-index: 1;
    }

    /* Student info card */
    .student-info-card {
      background-color: white;
      border-radius: 0.75rem;
      box-shadow: var(--card-shadow);
      margin-bottom: 1.5rem;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .student-info-card:hover {
      box-shadow: var(--hover-shadow);
      transform: translateY(-5px);
    }

    .student-info-card .card-header {
      background-color: var(--info-color);
      color: white;
      padding: 1rem 1.5rem;
      font-weight: 600;
      border: none;
    }

    .student-info-card .card-body {
      padding: 1.5rem;
    }

    .student-info-card p {
      margin-bottom: 0.75rem;
      display: flex;
      align-items: center;
    }

    .student-info-card p i {
      width: 24px;
      margin-right: 10px;
      color: var(--primary-color);
    }

    .student-info-card p strong {
      margin-right: 8px;
      color: var(--dark-color);
    }

    /* Course table styles */
    .course-table {
      background-color: white;
      border-radius: 0.75rem;
      box-shadow: var(--card-shadow);
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .course-table:hover {
      box-shadow: var(--hover-shadow);
    }

    .course-table .card-header {
      background: linear-gradient(to right, var(--primary-color), var(--accent-color));
      color: white;
      padding: 1rem 1.5rem;
      font-weight: 600;
      border: none;
    }

    .course-table .table {
      margin-bottom: 0;
    }

    .course-table th {
      background-color: rgba(67, 97, 238, 0.05);
      color: var(--dark-color);
      font-weight: 600;
      border-bottom: 1px solid #e3e6f0;
      padding: 1rem;
    }

    .course-table td {
      padding: 1rem;
      vertical-align: middle;
      border-bottom: 1px solid #e3e6f0;
    }

    .course-table tbody tr:hover {
      background-color: rgba(67, 97, 238, 0.05);
    }

    /* Status badges */
    .status-paid {
      background-color: rgba(28, 200, 138, 0.1);
      color: var(--success-color);
      font-weight: 600;
      padding: 0.35rem 0.65rem;
      border-radius: 0.35rem;
      display: inline-block;
    }

    .status-not-paid {
      background-color: rgba(231, 74, 59, 0.1);
      color: var(--danger-color);
      font-weight: 600;
      padding: 0.35rem 0.65rem;
      border-radius: 0.35rem;
      display: inline-block;
    }

    /* Action buttons */
    .btn-pay {
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 0.35rem;
      padding: 0.375rem 0.75rem;
      font-weight: 600;
      transition: all 0.2s;
    }

    .btn-pay:hover {
      background-color: var(--secondary-color);
      transform: translateY(-2px);
      color: white;
    }

    .btn-pay i {
      margin-right: 5px;
    }

    /* Alerts */
    .alert {
      border-radius: 0.75rem;
      padding: 1.25rem;
      margin-bottom: 1.5rem;
      border: none;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .alert-info {
      background-color: rgba(54, 185, 204, 0.1);
      color: var(--info-color);
    }

    .alert-warning {
      background-color: rgba(246, 194, 62, 0.1);
      color: var(--warning-color);
    }

    .alert-danger {
      background-color: rgba(231, 74, 59, 0.1);
      color: var(--danger-color);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .content {
        margin-left: 0;
        padding: 15px;
      }

      .welcome-header {
        padding: 1.5rem;
      }

      .student-info-card, .course-table {
        margin-bottom: 1rem;
      }
    }
  </style>
</head>
<body>

<?php
// Set current page for sidebar highlighting
$currentPage = basename($_SERVER['PHP_SELF']);
include_once __DIR__ . '/../../assets/parent_sidebar.php';
?>
  <!-- Main Content -->
  <div class="content">
    <div class="welcome-header">
      <h2><i class="fas fa-user-circle me-2"></i>Welcome, <?php echo htmlspecialchars($_SESSION['user']['user_name'] ?? 'Parent'); ?>!</h2>
      <p>Monitor your child's academic progress and manage payments</p>
    </div>

    <?php
    // Get parent's child student ID
    $parent_id = $_SESSION['user_id'] ?? 0;
    $stmt = $conn->prepare("SELECT child_student_id FROM parents WHERE user_id = ?");
    $stmt->bind_param("i", $parent_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
      $row = $result->fetch_assoc();
      $student_id = $row['child_student_id'];

      // Get student information
      $stmt = $conn->prepare("SELECT CONCAT(u.first_name, ' ', u.last_name) as student_name, s.grade
                           FROM students s
                           JOIN users u ON s.user_id = u.id
                           WHERE s.student_id = ?");
      $stmt->bind_param("i", $student_id);
      $stmt->execute();
      $student_result = $stmt->get_result();
      $student_info = $student_result->fetch_assoc();

      // Get student's courses and payment status
      $stmt = $conn->prepare("SELECT
                                sub.subject_name,
                                sub.subject_id,
                                s.payment_status,
                                s.last_payment_date
                              FROM
                                students s
                              JOIN
                                student_subjects ss ON s.student_id = ss.student_id
                              JOIN
                                subjects sub ON ss.subject_id = sub.subject_id
                              WHERE
                                s.student_id = ?");
      $stmt->bind_param("i", $student_id);
      $stmt->execute();
      $courses_result = $stmt->get_result();

      if ($student_info) {
        echo '<div class="student-info-card">
                <div class="card-header">
                  <i class="fas fa-user-graduate me-2"></i>Student Information
                </div>
                <div class="card-body">
                  <p><i class="fas fa-user"></i><strong>Name:</strong> ' . htmlspecialchars($student_info['student_name']) . '</p>
                  <p><i class="fas fa-graduation-cap"></i><strong>Grade:</strong> ' . htmlspecialchars($student_info['grade']) . '</p>
                  <p><i class="fas fa-id-card"></i><strong>Student ID:</strong> ' . htmlspecialchars($student_id) . '</p>
                </div>
              </div>';
      }

      if ($courses_result->num_rows > 0) {
        echo '<div class="course-table">
                <div class="card-header">
                  <i class="fas fa-book me-2"></i>Registered Courses & Payment Status
                </div>
                <div class="table-responsive">
                  <table class="table table-hover mb-0">
                    <thead>
                      <tr>
                        <th><i class="fas fa-book-open me-2"></i>Subject</th>
                        <th><i class="fas fa-money-check-alt me-2"></i>Payment Status</th>
                        <th><i class="fas fa-calendar-alt me-2"></i>Last Payment Date</th>
                        <th><i class="fas fa-tasks me-2"></i>Action</th>
                      </tr>
                    </thead>
                    <tbody>';

        while ($course = $courses_result->fetch_assoc()) {
          $status_class = $course['payment_status'] == 'PAID' ? 'status-paid' : 'status-not-paid';
          $status_text = $course['payment_status'] == 'PAID' ? 'Paid' : 'Not Paid';
          $payment_date = $course['last_payment_date'] ? date('d M Y', strtotime($course['last_payment_date'])) : '-';

          echo '<tr>
                  <td>' . htmlspecialchars($course['subject_name']) . '</td>
                  <td><span class="' . $status_class . '">' . $status_text . '</span></td>
                  <td>' . $payment_date . '</td>
                  <td>';

          if ($course['payment_status'] != 'PAID') {
            echo '<a href="../payments/parent_payment.php?student_id=' . $student_id . '&subject_id=' . $course['subject_id'] . '" class="btn btn-pay">
                    <i class="fas fa-credit-card"></i> Pay Now
                  </a>';
          } else {
            echo '<span class="text-bg-secondary bg-opacity-10 text-secondary px-3 py-2 rounded-2">Payment Complete</span>';
          }

          echo '</td>
                </tr>';
        }

        echo '</tbody>
              </table>
            </div>
          </div>';
      } else {
        echo '<div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>No courses found</strong> for this student. Please contact the administrator to enroll in courses.
              </div>';
      }
    } else {
      echo '<div class="alert alert-danger">
              <i class="fas fa-exclamation-circle me-2"></i>
              <strong>No student information found.</strong> Please contact the administrator to link your account with your child.
            </div>';
    }
    ?>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
