<?php
// Include authentication check
require_once '../../backend/server/auth_check.php';

// Set current page for sidebar highlighting
$currentPage = basename($_SERVER['PHP_SELF']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Worker Dashboard - Gurukula Institution</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome for icons -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <!-- Google Fonts - Poppins -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #1e3c72;
      --secondary-color: #2a5298;
      --accent-color: #67BAFD;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-color: #f8f9fa;
      --dark-color: #343a40;
      --border-radius: 15px;
      --card-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      --transition-speed: 0.3s;
    }

    body {
      background-color: #f4f7fe;
      font-family: 'Poppins', sans-serif;
      font-size: 1rem;
      color: #444;
    }

    /* Main content styles */
    .content {
      margin-left: 260px;
      padding: 30px;
      transition: all var(--transition-speed) ease;
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
      padding-bottom: 15px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .welcome-message {
      font-size: 1.8rem;
      font-weight: 600;
      color: var(--primary-color);
      margin: 0;
    }

    .date-display {
      font-size: 1rem;
      color: #6c757d;
    }

    /* Dashboard cards styles */
    .dashboard-card {
      background: white;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
      padding: 25px;
      transition: all var(--transition-speed) ease;
      height: 100%;
      border: none;
      overflow: hidden;
      position: relative;
    }

    .dashboard-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }

    .card-header {
      background: transparent;
      border-bottom: none;
      padding: 0 0 15px 0;
      font-weight: 600;
      font-size: 1.2rem;
      color: var(--primary-color);
      display: flex;
      align-items: center;
    }

    .card-header i {
      margin-right: 10px;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 10px;
      font-size: 1rem;
    }

    .card-header.bg-primary {
      background: transparent !important;
    }

    .card-header.bg-success {
      background: transparent !important;
    }

    .chart-container {
      height: 300px;
      margin-bottom: 20px;
      position: relative;
    }

    .stat-card {
      border-radius: var(--border-radius);
      padding: 20px;
      margin-bottom: 20px;
      background: white;
      box-shadow: var(--card-shadow);
      display: flex;
      align-items: center;
      transition: all var(--transition-speed) ease;
    }

    .stat-card:hover {
      transform: translateY(-5px);
    }

    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      font-size: 1.5rem;
    }

    .stat-icon.primary {
      background: linear-gradient(135deg, #4e73df, #224abe);
      color: white;
    }

    .stat-icon.success {
      background: linear-gradient(135deg, #1cc88a, #13855c);
      color: white;
    }

    .stat-icon.warning {
      background: linear-gradient(135deg, #f6c23e, #dda20a);
      color: white;
    }

    .stat-info h3 {
      font-size: 1.8rem;
      font-weight: 700;
      margin: 0;
      color: #5a5c69;
    }

    .stat-info p {
      margin: 0;
      color: #858796;
      font-size: 0.9rem;
    }

    #refreshAttendance {
      width: 32px;
      height: 32px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.3s ease;
    }

    #refreshAttendance:hover {
      transform: rotate(30deg);
    }

    #todayAttendance {
      transition: color 0.3s ease;
    }

    #todayAttendance.text-success {
      color: #28a745 !important;
      font-weight: bold;
    }

    .list-group-item {
      border: none;
      border-radius: 10px;
      margin-bottom: 8px;
      transition: all var(--transition-speed) ease;
    }

    .list-group-item:hover {
      background-color: #f8f9fa;
      transform: translateX(5px);
    }

    @media (max-width: 768px) {
      .content {
        margin-left: 0;
        padding: 15px;
      }

      .welcome-message {
        font-size: 1.5rem;
      }
    }
  </style>
</head>
<body>

<?php
include_once __DIR__ . '/../../assets/worker_sidebar.php';
?>

  <!-- Main Content -->
  <div class="content">
    <div class="page-header">
      <div>
        <?php
        // Get first name from the session or database
        $user_id = $_SESSION['user_id'] ?? 0;
        $first_name = '';

        // Include database connection if not already included
        if (!isset($conn)) {
          include_once __DIR__ . '/../../backend/server/db_connect.php';
        }

        // Get first name from database
        $stmt = $conn->prepare("SELECT first_name FROM users WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
          $first_name = $row['first_name'];
        }
        $stmt->close();
        ?>
        <h1 class="welcome-message">Welcome, <?php echo htmlspecialchars($first_name ?: ($_SESSION['user']['user_name'] ?? 'Worker')); ?>!</h1>
        <p class="date-display"><?php echo date('l, F j, Y'); ?></p>
      </div>
    </div>

    <!-- Stats Overview -->
    <div class="row mb-4">
      <div class="col-xl-4 col-md-6 mb-4">
        <div class="stat-card">
          <div class="stat-icon primary">
            <i class="fas fa-user-graduate"></i>
          </div>
          <div class="stat-info">
            <?php
            // Get total students count
            $students_query = "SELECT COUNT(*) as total FROM students";
            $students_result = $conn->query($students_query);
            $total_students = 0;
            if ($students_result && $row = $students_result->fetch_assoc()) {
              $total_students = $row['total'];
            }
            ?>
            <h3 id="totalStudents"><?php echo $total_students; ?></h3>
            <p>Total Students</p>
          </div>
        </div>
      </div>

      <div class="col-xl-4 col-md-6 mb-4">
        <div class="stat-card">
          <div class="stat-icon success">
            <i class="fas fa-calendar-check"></i>
          </div>
          <div class="stat-info">
            <?php
            // Get today's attendance count
            $today = date('Y-m-d');
            $attendance_query = "SELECT COUNT(DISTINCT student_id) as total FROM attendance WHERE date = '$today'";
            $attendance_result = $conn->query($attendance_query);
            $today_attendance = 0;
            if ($attendance_result && $row = $attendance_result->fetch_assoc()) {
              $today_attendance = $row['total'];
            }
            ?>
            <div class="d-flex align-items-center">
              <h3 id="todayAttendance" class="me-2"><?php echo $today_attendance; ?></h3>
              <button id="refreshAttendance" class="btn btn-sm btn-outline-success" title="Refresh attendance count">
                <i class="fas fa-sync-alt"></i>
              </button>
            </div>
            <p>Today's Attendance</p>
          </div>
        </div>
      </div>

      <div class="col-xl-4 col-md-6 mb-4">
        <div class="stat-card">
          <div class="stat-icon warning">
            <i class="fas fa-money-bill-wave"></i>
          </div>
          <div class="stat-info">
            <?php
            // Get pending payments count
            $payments_query = "SELECT COUNT(*) as total FROM students WHERE payment_status != 'PAID'";
            $payments_result = $conn->query($payments_query);
            $pending_payments = 0;
            if ($payments_result && $row = $payments_result->fetch_assoc()) {
              $pending_payments = $row['total'];
            }
            ?>
            <h3 id="pendingPayments"><?php echo $pending_payments; ?></h3>
            <p>Pending Payments</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Subject-wise Attendance Chart -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="dashboard-card">
          <div class="card-header">
            <i class="fas fa-chart-bar"></i>
            <span>Subject-wise Attendance</span>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="subjectAttendanceChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-12 mb-4">
        <div class="dashboard-card">
          <div class="card-header">
            <i class="fas fa-list"></i>
            <span>Recent Attendance</span>
          </div>
          <div class="card-body">
            <div id="recentAttendance" class="list-group">
              <!-- Will be populated via AJAX -->
              <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
  // Make updateTodayAttendance function global so it can be called from other windows/frames
  function updateTodayAttendance() {
    console.log("Updating Today's Attendance count...");
    $.ajax({
      url: '../../backend/attendance/get_today_attendance.php',
      type: 'GET',
      success: function(data) {
        console.log("Today's Attendance count updated:", data.count);
        $('#todayAttendance').text(data.count);

        // Flash the counter to indicate it's been updated
        const $counter = $('#todayAttendance');
        $counter.addClass('text-success');
        setTimeout(function() {
          $counter.removeClass('text-success');
        }, 1000);
      },
      error: function() {
        console.error('Failed to update today\'s attendance count');
      }
    });
  }

  // Make the function available globally
  window.updateTodayAttendance = updateTodayAttendance;

  $(document).ready(function() {
    // Listen for localStorage changes from other tabs/windows
    window.addEventListener('storage', function(e) {
      if (e.key === 'attendance_updated') {
        // Update the attendance count when triggered from another tab/window
        updateTodayAttendance();
      }
    });

    // Update today's attendance count initially
    updateTodayAttendance();

    // Set up auto-refresh for today's attendance count (every 60 seconds)
    setInterval(updateTodayAttendance, 60000);

    // Handle refresh button click
    $('#refreshAttendance').click(function() {
      const $button = $(this);
      const $icon = $button.find('i');

      // Add spinning animation
      $icon.addClass('fa-spin');

      // Update attendance count
      $.ajax({
        url: '../../backend/attendance/get_today_attendance.php',
        type: 'GET',
        success: function(data) {
          $('#todayAttendance').text(data.count);

          // Show success indicator
          $button.removeClass('btn-outline-success').addClass('btn-success');
          setTimeout(function() {
            $button.removeClass('btn-success').addClass('btn-outline-success');
            $icon.removeClass('fa-spin');
          }, 1000);
        },
        error: function() {
          console.error('Failed to update today\'s attendance count');
          $icon.removeClass('fa-spin');

          // Show error indicator
          $button.removeClass('btn-outline-success').addClass('btn-danger');
          setTimeout(function() {
            $button.removeClass('btn-danger').addClass('btn-outline-success');
          }, 1000);
        }
      });
    });

    // Load recent attendance with improved styling
    $.ajax({
      url: '../../backend/attendance/get_recent_attendance.php',
      type: 'GET',
      success: function(data) {
        let html = '';
        if (data.length > 0) {
          data.forEach(item => {
            const date = new Date(item.date + ' ' + item.time);
            const formattedDate = date.toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            });

            html += `
              <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between align-items-center">
                  <div>
                    <h6 class="mb-1 fw-bold">${item.student_name}</h6>
                    <div class="d-flex align-items-center">
                      <span class="badge bg-primary me-2">Grade ${item.grade}</span>
                      <span class="badge bg-info me-2">${item.subject_name}</span>
                    </div>
                  </div>
                  <small class="text-muted">${formattedDate}</small>
                </div>
              </div>
            `;
          });

          // Update today's attendance count after loading recent attendance
          updateTodayAttendance();
        } else {
          html = '<div class="alert alert-info py-3 text-center">No recent attendance records</div>';
        }
        $('#recentAttendance').html(html);
      },
      error: function() {
        $('#recentAttendance').html('<div class="alert alert-danger py-3 text-center">Failed to load attendance data</div>');
      }
    });

    // Initialize subject-wise attendance chart with modern styling
    const subjectCtx = document.getElementById('subjectAttendanceChart').getContext('2d');
    const subjectChart = new Chart(subjectCtx, {
      type: 'bar',
      data: {
        labels: [], // Will be populated from AJAX
        datasets: [{
          label: 'Number of Students',
          data: [], // Will be populated from AJAX
          backgroundColor: 'rgba(78, 115, 223, 0.6)',
          borderColor: 'rgba(78, 115, 223, 1)',
          borderWidth: 2,
          borderRadius: 5,
          maxBarThickness: 40
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            padding: 12,
            titleFont: {
              size: 14,
              weight: 'bold'
            },
            bodyFont: {
              size: 13
            },
            displayColors: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              precision: 0,
              font: {
                size: 12
              }
            },
            grid: {
              color: 'rgba(0, 0, 0, 0.05)'
            },
            title: {
              display: true,
              text: 'Number of Students',
              font: {
                size: 14,
                weight: 'bold'
              },
              padding: {
                top: 10,
                bottom: 10
              }
            }
          },
          x: {
            grid: {
              display: false
            },
            ticks: {
              font: {
                size: 12
              }
            },
            title: {
              display: true,
              text: 'Subjects',
              font: {
                size: 14,
                weight: 'bold'
              },
              padding: {
                top: 10,
                bottom: 10
              }
            }
          }
        }
      }
    });

    // Load subject-wise attendance data
    $.ajax({
      url: '../../backend/attendance/get_subject_attendance.php',
      type: 'GET',
      success: function(data) {
        subjectChart.data.labels = data.subjects;
        subjectChart.data.datasets[0].data = data.counts;

        // Generate dynamic colors based on number of subjects
        const colors = [];
        const borderColors = [];
        for (let i = 0; i < data.subjects.length; i++) {
          const hue = (i * 137) % 360; // Use golden ratio to spread colors
          colors.push(`hsla(${hue}, 70%, 60%, 0.6)`);
          borderColors.push(`hsla(${hue}, 70%, 50%, 1)`);
        }

        subjectChart.data.datasets[0].backgroundColor = colors;
        subjectChart.data.datasets[0].borderColor = borderColors;

        subjectChart.update();
      },
      error: function() {
        console.error('Failed to load subject attendance data');
      }
    });
  });
  </script>
</body>
</html>
