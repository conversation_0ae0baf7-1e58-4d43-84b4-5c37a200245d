@echo off
echo Fixing XAMPP permissions...

REM Run as administrator
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"
if '%errorlevel%' NEQ '0' (
    echo Requesting administrative privileges...
    goto UACPrompt
) else ( goto gotAdmin )

:UACPrompt
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "", "", "runas", 1 >> "%temp%\getadmin.vbs"
    "%temp%\getadmin.vbs"
    exit /B

:gotAdmin
    if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )
    pushd "%CD%"
    CD /D "%~dp0"

REM Stop all XAMPP services
echo Stopping all XAMPP services...
taskkill /F /IM httpd.exe /T > nul 2>&1
taskkill /F /IM mysqld.exe /T > nul 2>&1

REM Fix MySQL data directory permissions
echo Setting permissions for MySQL data directory...
icacls "C:\xampp\mysql\data" /grant Everyone:(OI)(CI)F /T
icacls "C:\xampp\mysql\data\*" /grant Everyone:F /T

REM Fix Apache logs directory permissions
echo Setting permissions for Apache logs directory...
icacls "C:\xampp\apache\logs" /grant Everyone:(OI)(CI)F /T

REM Fix temp directory permissions
echo Setting permissions for temp directory...
icacls "C:\xampp\tmp" /grant Everyone:(OI)(CI)F /T

REM Check for port conflicts
echo Checking for port conflicts...
netstat -ano | findstr :80
netstat -ano | findstr :443
netstat -ano | findstr :3306

echo.
echo Permissions have been fixed. You can now start XAMPP services.
echo.
echo Press any key to exit...
pause > nul
