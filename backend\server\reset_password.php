<?php
session_start();
include_once 'db_connect.php';

$message = '';
$message_type = '';
$token_valid = false;
$token = '';
$user_id = null;

// Check if token is provided
if (isset($_GET['token']) && !empty($_GET['token'])) {
    $token = $_GET['token'];

    // Process the token

    // First check if the token exists and is valid
    $token_stmt = $conn->prepare("
        SELECT user_id, expires_at, used
        FROM password_reset_tokens
        WHERE token = ?
    ");
    $token_stmt->bind_param("s", $token);
    $token_stmt->execute();
    $token_result = $token_stmt->get_result();

    if ($token_result->num_rows > 0) {
        $token_data = $token_result->fetch_assoc();

        // Check if token is expired
        if (strtotime($token_data['expires_at']) < time()) {
            $message = "This password reset link has expired. Please request a new one.";
            $message_type = "danger";
        }
        // Check if token is already used
        else if ($token_data['used'] == 1) {
            $message = "This password reset link has already been used. Please request a new one if needed.";
            $message_type = "danger";
        }
        else {
            // Token is valid, now check if user exists
            $user_id = $token_data['user_id'];
            $user_stmt = $conn->prepare("SELECT first_name FROM users WHERE id = ?");
            $user_stmt->bind_param("i", $user_id);
            $user_stmt->execute();
            $user_result = $user_stmt->get_result();

            if ($user_result->num_rows > 0) {
                $user_data = $user_result->fetch_assoc();
                $first_name = $user_data['first_name'];
                $token_valid = true;
            } else {
                $message = "The user account associated with this reset link no longer exists.";
                $message_type = "danger";
            }
            $user_stmt->close();
        }
    } else {
        $message = "Invalid password reset link. Please request a new password reset link.";
        $message_type = "danger";
    }
    $token_stmt->close();
} else {
    $message = "No token provided. Please request a password reset link.";
    $message_type = "danger";
}

// Process password reset form
if ($_SERVER["REQUEST_METHOD"] == "POST" && $token_valid) {
    $password = trim($_POST['password']);
    $confirm_password = trim($_POST['confirm_password']);

    // Validate password
    $password_errors = [];

    // Check length
    if (strlen($password) < 8) {
        $password_errors[] = "Password must be at least 8 characters long";
    }

    // Check for letters
    if (!preg_match('/[a-zA-Z]/', $password)) {
        $password_errors[] = "Password must include at least one letter";
    }

    // Check for numbers
    if (!preg_match('/\d/', $password)) {
        $password_errors[] = "Password must include at least one number";
    }

    // Check for special characters
    if (!preg_match('/[^a-zA-Z0-9]/', $password)) {
        $password_errors[] = "Password must include at least one special character";
    }

    // Check if passwords match
    if ($password !== $confirm_password) {
        $password_errors[] = "Passwords do not match";
    }

    if (empty($password_errors)) {
        // Hash the new password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        // Update the user's password
        $update_stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
        $update_stmt->bind_param("si", $hashed_password, $user_id);

        if ($update_stmt->execute()) {
            // Mark the token as used
            $token_stmt = $conn->prepare("UPDATE password_reset_tokens SET used = 1 WHERE token = ?");
            $token_stmt->bind_param("s", $token);
            $token_stmt->execute();
            $token_stmt->close();

            $message = "Your password has been reset successfully. You can now <a href='../../frontend/login.php'>login</a> with your new password.";
            $message_type = "success";
            $token_valid = false; // Hide the form
        } else {
            $message = "An error occurred. Please try again later.";
            $message_type = "danger";
        }

        $update_stmt->close();
    } else {
        $message = "Password requirements not met: " . implode(", ", $password_errors);
        $message_type = "danger";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Gurukula LMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --light-bg: #f8f9fa;
            --dark-text: #2d3748;
            --light-text: #718096;
            --card-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }

        .reset-password-container {
            width: 100%;
            max-width: 450px;
        }

        .card {
            border-radius: 20px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
        }

        .card-header::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 0;
            right: 0;
            margin: 0 auto;
            width: 40px;
            height: 40px;
            background: white;
            transform: rotate(45deg);
            z-index: 1;
        }

        .card-header h3 {
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .card-header p {
            opacity: 0.8;
            margin-bottom: 0;
        }

        .card-body {
            padding: 2.5rem 2rem 2rem;
            position: relative;
            z-index: 2;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-floating > .form-control {
            padding: 1.5rem 1rem;
            height: calc(3.5rem + 2px);
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .form-floating > .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
        }

        .form-floating > label {
            padding: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: 12px;
            padding: 1rem;
            font-size: 1rem;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px -5px rgba(67, 97, 238, 0.4);
        }

        .footer-links {
            text-align: center;
            margin-top: 1.5rem;
        }

        .footer-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            margin: 0 1rem;
            transition: all 0.3s ease;
        }

        .footer-links a:hover {
            color: rgba(255, 255, 255, 0.8);
        }

        .alert {
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .password-strength {
            height: 5px;
            background-color: #e2e8f0;
            border-radius: 5px;
            margin-top: 0.5rem;
            overflow: hidden;
        }

        .password-strength-bar {
            height: 100%;
            width: 0;
            transition: width 0.3s ease;
        }

        .password-feedback {
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        .password-requirements {
            margin-top: 1rem;
            font-size: 0.85rem;
            color: var(--light-text);
        }

        .password-requirements ul {
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="reset-password-container">
        <div class="card">
            <div class="card-header">
                <h3>Reset Password</h3>
                <p>Create a new password for your account</p>
            </div>
            <div class="card-body">
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?>">
                        <?php echo $message; ?>
                        <?php if ($message_type === 'danger' && !$token_valid): ?>
                            <div class="mt-3">
                                <a href="forgot_password.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-redo me-1"></i> Request New Reset Link
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <?php if ($token_valid): ?>
                    <form method="POST" action="reset_password.php?token=<?php echo $token; ?>">
                        <div class="form-floating mb-3">
                            <input type="password" name="password" id="password" class="form-control" placeholder="New password" required>
                            <label for="password"><i class="fas fa-lock me-2"></i>New Password</label>
                            <div class="password-strength mt-2">
                                <div class="password-strength-bar" id="passwordStrengthBar"></div>
                            </div>
                            <div class="password-feedback" id="passwordFeedback"></div>
                        </div>

                        <div class="form-floating mb-4">
                            <input type="password" name="confirm_password" id="confirm_password" class="form-control" placeholder="Confirm new password" required>
                            <label for="confirm_password"><i class="fas fa-lock me-2"></i>Confirm New Password</label>
                        </div>

                        <div class="password-requirements">
                            <small>
                                <i class="fas fa-info-circle me-1"></i> Password must:
                                <ul class="mb-0 ps-4 mt-1">
                                    <li>Be at least 8 characters long</li>
                                    <li>Include at least one letter</li>
                                    <li>Include at least one number</li>
                                    <li>Include at least one special character (e.g., !@#$%^&*)</li>
                                </ul>
                            </small>
                        </div>

                        <button type="submit" class="btn btn-primary mt-4">
                            <i class="fas fa-key me-2"></i>Reset Password
                        </button>
                    </form>
                <?php endif; ?>
            </div>
        </div>

        <div class="footer-links">
            <a href="../../frontend/login.php"><i class="fas fa-arrow-left me-1"></i>Back to Login</a>
        </div>
    </div>

    <script>
        // Password strength meter
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('password');
            if (passwordInput) {
                const strengthBar = document.getElementById('passwordStrengthBar');
                const feedback = document.getElementById('passwordFeedback');

                passwordInput.addEventListener('input', function() {
                    const password = this.value;
                    let strength = 0;
                    let message = '';

                    if (password.length > 0) {
                        // Length check
                        if (password.length >= 8) strength += 25;

                        // Uppercase and lowercase check
                        if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength += 25;

                        // Number check
                        if (password.match(/\d/)) strength += 25;

                        // Special character check
                        if (password.match(/[^a-zA-Z0-9]/)) strength += 25;

                        // Update strength bar
                        strengthBar.style.width = strength + '%';

                        // Set color and message based on strength
                        if (strength < 25) {
                            strengthBar.style.backgroundColor = '#e74c3c';
                            message = '<span class="text-danger">Very Weak</span>';
                        } else if (strength < 50) {
                            strengthBar.style.backgroundColor = '#f39c12';
                            message = '<span class="text-warning">Weak</span>';
                        } else if (strength < 75) {
                            strengthBar.style.backgroundColor = '#3498db';
                            message = '<span class="text-info">Medium</span>';
                        } else {
                            strengthBar.style.backgroundColor = '#2ecc71';
                            message = '<span class="text-success">Strong</span>';
                        }
                    }

                    feedback.innerHTML = message;
                });
            }
        });
    </script>
</body>
</html>
