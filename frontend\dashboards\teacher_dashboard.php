<?php
// Include authentication check
require_once '../../backend/server/auth_check.php';

// Get user data for display
$user_id = $_SESSION['user_id'];

// Include database connection
include_once '../../backend/server/db_connect.php';

// Get first name and last name from database
$stmt = $conn->prepare("SELECT first_name, last_name FROM users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

// Set user name from database or default to 'Teacher'
$user_name = ($user) ? $user['first_name'] . ' ' . $user['last_name'] : 'Teacher';
?>
<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
  <meta charset="UTF-8">
  <title>Teacher Dashboard - Gurukula Institution</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome for icons -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-bg: #f4f6f9;
      --dark-text: #2d3748;
      --light-text: #718096;
      --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: var(--light-bg);
      color: var(--dark-text);
    }

    /* Main content styles */
    .content {
      margin-left: 280px;
      padding: 30px;
    }

    .welcome-section {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
    }

    .welcome-section::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 200%;
      background: rgba(255, 255, 255, 0.1);
      transform: rotate(30deg);
    }

    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .stat-item {
      background: white;
      border-radius: 16px;
      padding: 1.5rem;
      box-shadow: var(--card-shadow);
      transition: all 0.3s ease;
      border-left: 5px solid var(--primary-color);
    }

    .stat-item:hover {
      transform: translateY(-5px);
      box-shadow: var(--hover-shadow);
    }

    .stat-item i {
      font-size: 2rem;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .stat-item h4 {
      font-size: 1.75rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }

    .stat-item p {
      color: var(--light-text);
      margin-bottom: 0;
    }

    .dashboard-card {
      background-color: white;
      border-radius: 16px;
      box-shadow: var(--card-shadow);
      text-align: center;
      padding: 2rem;
      transition: all 0.3s ease;
      margin-bottom: 1.5rem;
      height: 100%;
      border-top: 5px solid var(--primary-color);
    }

    .dashboard-card:hover {
      transform: translateY(-5px);
      box-shadow: var(--hover-shadow);
    }

    .dashboard-card i {
      font-size: 2.5rem;
      color: var(--primary-color);
      margin-bottom: 1rem;
      transition: all 0.3s ease;
    }

    .dashboard-card:hover i {
      transform: scale(1.1);
    }

    .dashboard-card h5 {
      font-size: 1.25rem;
      margin-bottom: 0.5rem;
      font-weight: 600;
      color: var(--dark-text);
    }

    .dashboard-card p {
      color: var(--light-text);
      font-size: 0.9rem;
    }

    .recent-activity {
      background: white;
      border-radius: 16px;
      padding: 1.5rem;
      box-shadow: var(--card-shadow);
      margin-bottom: 2rem;
    }

    .activity-item {
      padding: 1rem;
      border-left: 3px solid var(--primary-color);
      background-color: rgba(67, 97, 238, 0.05);
      border-radius: 0 8px 8px 0;
      margin-bottom: 1rem;
    }

    .activity-item:last-child {
      margin-bottom: 0;
    }

    .activity-item h6 {
      font-weight: 600;
      margin-bottom: 0.25rem;
    }

    .activity-item p {
      color: var(--light-text);
      margin-bottom: 0;
      font-size: 0.9rem;
    }

    .activity-time {
      font-size: 0.8rem;
      color: var(--light-text);
    }

    .card-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 1.5rem;
    }

    .section-title {
      font-weight: 600;
      margin-bottom: 1.5rem;
      color: var(--dark-text);
    }

    @media (max-width: 992px) {
      .content {
        margin-left: 0;
        padding: 20px;
      }
    }
  </style>
</head>
<body>

<?php include_once __DIR__ . '/../../assets/teacher_sidebar.php'; ?>

  <!-- Main Content -->
  <div class="content">
    <div class="welcome-section">
      <h2 class="mb-2">Welcome, <?php echo htmlspecialchars($user_name); ?>!</h2>
      <p class="mb-0">Manage your classes, assignments, and track student progress</p>
    </div>

    <?php if (isset($uploadSuccess) && $uploadSuccess): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
      <strong>Success!</strong> Your profile image has been updated.
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($uploadError) && $uploadError): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
      <strong>Error!</strong> <?php echo htmlspecialchars($uploadError); ?>
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <!-- Teacher Profile Section -->
    <div class="profile-section mb-4">
      <?php
      // Fetch teacher details
      $stmt = $conn->prepare("
          SELECT u.first_name, u.last_name, u.email, t.phone, t.profile_image,
                 GROUP_CONCAT(s.subject_name SEPARATOR ', ') as subject_names
          FROM users u
          JOIN teachers t ON u.id = t.user_id
          LEFT JOIN teacher_subjects ts ON t.teacher_id = ts.teacher_id
          LEFT JOIN subjects s ON ts.subject_id = s.subject_id
          WHERE u.id = ?
          GROUP BY u.id, t.teacher_id
      ");
      $stmt->bind_param("i", $user_id);
      $stmt->execute();
      $result = $stmt->get_result();
      $teacher = $result->fetch_assoc();
      $stmt->close();

      // Set default image if none exists
      $imagePath = isset($teacher['profile_image']) && $teacher['profile_image']
          ? '../../' . $teacher['profile_image']
          : '../../assets/default.jpg';

      // Check for upload messages
      $uploadSuccess = isset($_SESSION['upload_success']) ? $_SESSION['upload_success'] : false;
      $uploadError = isset($_SESSION['upload_error']) ? $_SESSION['upload_error'] : '';

      // Clear session messages after reading them
      if (isset($_SESSION['upload_success'])) unset($_SESSION['upload_success']);
      if (isset($_SESSION['upload_error'])) unset($_SESSION['upload_error']);
      ?>

      <div class="card border-0 shadow-sm rounded-4">
        <div class="card-body p-4">
          <div class="row align-items-center">
            <div class="col-md-3 text-center">
              <!-- Profile Image Upload -->
              <form action="../../backend/teacher/upload_image.php" method="POST" enctype="multipart/form-data" id="uploadForm">
                <label for="imageUpload" class="mb-0" style="cursor: pointer;">
                  <img src="<?= htmlspecialchars($imagePath) ?>" alt="Profile Picture" class="rounded-circle shadow"
                       style="width: 120px; height: 120px; object-fit: cover; border: 3px solid var(--primary-color); transition: all 0.3s ease;">
                  <div class="mt-2 text-center">
                    <small class="text-muted"><i class="fas fa-camera me-1"></i>Click to change</small>
                  </div>
                </label>
                <!-- Debug info - uncomment if needed -->
                <!-- <div class="mt-2 small text-muted">Image path: <?= htmlspecialchars($imagePath) ?></div> -->
                <input type="file" name="profile_image" id="imageUpload" accept="image/*" style="display: none" onchange="document.getElementById('uploadForm').submit();">
                <input type="hidden" name="user_id" value="<?= $user_id ?>">
              </form>
            </div>
            <div class="col-md-9">
              <h3 class="fw-bold mb-3"><?= htmlspecialchars($teacher['first_name'] . ' ' . $teacher['last_name']) ?></h3>
              <div class="d-flex align-items-center mb-2">
                <div class="bg-light rounded-pill p-2 me-3" style="width: 36px; height: 36px; display: flex; align-items: center; justify-content: center;">
                  <i class="fas fa-envelope text-primary"></i>
                </div>
                <p class="mb-0"><?= htmlspecialchars($teacher['email']) ?></p>
              </div>
              <div class="d-flex align-items-center mb-2">
                <div class="bg-light rounded-pill p-2 me-3" style="width: 36px; height: 36px; display: flex; align-items: center; justify-content: center;">
                  <i class="fas fa-phone text-primary"></i>
                </div>
                <p class="mb-0"><?= htmlspecialchars($teacher['phone'] ?? 'Not provided') ?></p>
              </div>
              <div class="d-flex align-items-center">
                <div class="bg-light rounded-pill p-2 me-3" style="width: 36px; height: 36px; display: flex; align-items: center; justify-content: center;">
                  <i class="fas fa-book text-primary"></i>
                </div>
                <p class="mb-0"><?= htmlspecialchars($teacher['subject_names'] ?: 'No subjects assigned') ?></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


    <h4 class="section-title">Quick Actions</h4>
    <div class="card-container">
      <!-- Add Subject Card -->
      <a href="../../frontend/teacher/add_subject.php" class="text-decoration-none">
        <div class="dashboard-card">
          <i class="fas fa-book"></i>
          <h5>Add Subject to Teach</h5>
          <p>Register new subjects that you'll be teaching</p>
        </div>
      </a>

      <!-- View All Subjects Card -->
      <a href="../../frontend/teacher/view_all_subjects.php" class="text-decoration-none">
        <div class="dashboard-card">
          <i class="fas fa-list"></i>
          <h5>View All Subjects</h5>
          <p>See all available subjects and their details</p>
        </div>
      </a>

      <!-- Manage Fees Card -->
      <a href="../../frontend/teacher/manage_fees.php" class="text-decoration-none">
        <div class="dashboard-card">
          <i class="fas fa-money-bill-wave"></i>
          <h5>Manage Subject Fees</h5>
          <p>Set and update fees for subjects you teach</p>
        </div>
      </a>
    </div>

  <!-- Notification Icon -->
  <div class="position-fixed top-0 end-0 p-3" style="z-index: 10000;">
    <button class="btn position-relative" id="notificationBtn">
      <i class="fas fa-bell fa-lg"></i>
      <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notificationCount">
        0
      </span>
    </button>

    <!-- Notification dropdown box -->
    <div id="notificationBox" class="card shadow" style="display: none;">
      <div class="card-header bg-primary text-white">
        <i class="fas fa-bell me-2"></i><strong>Notifications</strong>
      </div>
      <ul class="list-group list-group-flush" id="notificationList"></ul>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="../../assets/js/notifications.js"></script>

  <style>
    #notificationBtn {
      background: white;
      border: none;
      width: 42px;
      height: 42px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }

    #notificationBtn:hover {
      background-color: #f8f9fa;
      transform: translateY(-2px);
    }

    #notificationBox {
      width: 320px;
      border-radius: 12px;
      box-shadow: 0 10px 25px rgba(0,0,0,0.15);
      max-height: 400px;
      overflow-y: auto;
      position: absolute;
      right: 0;
      top: 100%;
      margin-top: 10px;
    }

    #notificationBox .card-header {
      padding: 1rem 1.25rem;
    }

    #notificationList {
      max-height: 300px;
      overflow-y: auto;
    }

    #notificationList .list-group-item {
      padding: 1rem 1.25rem;
      border-left: none;
      border-right: none;
    }

    .notification-item {
      transition: background-color 0.2s;
    }

    .notification-item:hover {
      background-color: #f8f9fa;
    }

    .notification-title {
      font-weight: 500;
      margin-bottom: 5px;
    }

    .notification-time {
      font-size: 0.8rem;
      color: #6c757d;
    }

    .bg-light-warning {
      background-color: #fff8e1;
    }
  </style>
</body>
</html>
