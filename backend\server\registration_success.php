<?php
session_start();

// Check if QR code should be shown
if (!isset($_SESSION['show_qr']) || $_SESSION['show_qr'] !== true) {
    header("Location: login.php");
    exit();
}

$student_id = $_SESSION['student_id'] ?? '';
$qr_image = $_SESSION['qr_image'] ?? '';
$qr_pdf = $_SESSION['qr_pdf'] ?? '';
$qr_error = $_SESSION['qr_error'] ?? '';
$has_qr = !empty($qr_image) && !empty($qr_pdf);

// Clear the session variables after displaying
$_SESSION['show_qr'] = false;
unset($_SESSION['student_id']);
unset($_SESSION['qr_image']);
unset($_SESSION['qr_pdf']);
unset($_SESSION['qr_error']);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Successful</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h3 class="mb-0">Registration Successful!</h3>
                    </div>
                    <div class="card-body text-center">
                        <?php if ($has_qr): ?>
                            <h4 class="mb-4">Your attendance QR code is ready</h4>

                            <div class="mb-4">
                                <img src="<?php echo $qr_image; ?>" alt="Student QR Code" class="img-thumbnail" style="max-width: 250px;">
                            </div>

                            <p class="mb-4">
                                This QR code will be used to mark your attendance. Please save it on your phone or print it.
                            </p>

                            <div class="d-grid gap-2 col-md-6 mx-auto">
                                <a href="<?php echo $qr_pdf; ?>" download class="btn btn-primary btn-lg">
                                    <i class="fas fa-download"></i> Download QR Code PDF
                                </a>
                                <a href="../../frontend/login.php" class="btn btn-outline-secondary mt-3">
                                    Proceed to Login
                                </a>
                            </div>
                        <?php else: ?>
                            <h4 class="mb-4">Registration Successful!</h4>

                            <?php if (!empty($qr_error)): ?>
                                <div class="alert alert-warning mb-4">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>QR Code Generation Failed:</strong> <?php echo $qr_error; ?>
                                </div>
                                <p class="mb-4">
                                    Your account has been created successfully, but we couldn't generate your attendance QR code.
                                    Please contact the administrator to enable the GD library in PHP.
                                </p>
                            <?php else: ?>
                                <div class="alert alert-warning mb-4">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>QR Code Generation Failed:</strong> The GD library is not enabled in PHP.
                                </div>
                                <p class="mb-4">
                                    Your account has been created successfully, but we couldn't generate your attendance QR code.
                                    Please contact the administrator to enable the GD library in PHP.
                                </p>
                            <?php endif; ?>

                            <div class="d-grid gap-2 col-md-6 mx-auto">
                                <a href="../../frontend/login.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i> Proceed to Login
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>