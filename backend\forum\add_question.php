<?php
require_once '../server/db_connect.php';
session_start();

// Debug: Log all received data
error_log("POST data: " . print_r($_POST, true));
error_log("Session data: " . print_r($_SESSION, true));

// Check if user is logged in and is a student
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    echo json_encode(['success' => false, 'message' => 'Not authorized']);
    exit;
}

// Check if all required fields are present
$missing_fields = [];
if (!isset($_POST['subject_id'])) $missing_fields[] = 'subject_id';
if (!isset($_POST['grade'])) $missing_fields[] = 'grade';
if (!isset($_POST['question'])) $missing_fields[] = 'question';

if (!empty($missing_fields)) {
    echo json_encode([
        'success' => false, 
        'message' => 'Missing required fields: ' . implode(', ', $missing_fields),
        'received' => $_POST
    ]);
    exit;
}

$student_id = $_SESSION['user_id'];
$subject_id = $_POST['subject_id'];
$grade = $_POST['grade'];
$question = $_POST['question'];

// Validate inputs
$empty_fields = [];
if (empty($subject_id)) $empty_fields[] = 'subject_id';
if (empty($grade)) $empty_fields[] = 'grade';
if (empty($question)) $empty_fields[] = 'question';

if (!empty($empty_fields)) {
    echo json_encode([
        'success' => false, 
        'message' => 'Empty required fields: ' . implode(', ', $empty_fields),
        'received' => [
            'subject_id' => $subject_id,
            'grade' => $grade,
            'question' => $question
        ]
    ]);
    exit;
}

// Get the student_id from the students table (not the user_id)
$stmt = $conn->prepare("SELECT student_id FROM students WHERE user_id = ?");
$stmt->bind_param("i", $student_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'Student record not found']);
    exit;
}

$student_data = $result->fetch_assoc();
$student_id = $student_data['student_id']; // Now using the actual student_id

// Insert the question
$stmt = $conn->prepare("INSERT INTO questions (student_id, subject_id, grade, question_text) VALUES (?, ?, ?, ?)");
$stmt->bind_param("iiss", $student_id, $subject_id, $grade, $question);

if ($stmt->execute()) {
    echo json_encode(['success' => true]);
} else {
    echo json_encode([
        'success' => false, 
        'message' => 'Database error: ' . $stmt->error,
        'query_info' => [
            'student_id' => $student_id,
            'subject_id' => $subject_id,
            'grade' => $grade,
            'question_length' => strlen($question)
        ]
    ]);
}

$stmt->close();
$conn->close();
?>
