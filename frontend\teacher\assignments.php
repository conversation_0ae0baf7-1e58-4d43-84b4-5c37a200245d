<?php include_once __DIR__ . '/../../backend/teacher/assignments.php'; ?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Assignments - <PERSON><PERSON>la</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-bg: #f4f6f9;
      --dark-text: #2d3748;
      --light-text: #718096;
      --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    body {
      background: var(--light-bg);
      font-family: 'Poppins', sans-serif;
      color: var(--dark-text);
    }

    .content {
      margin-left: 280px;
      padding: 30px;
    }

    .page-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
    }

    .page-header::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 200%;
      background: rgba(255, 255, 255, 0.1);
      transform: rotate(30deg);
    }

    .card {
      background: white;
      border-radius: 16px;
      box-shadow: var(--card-shadow);
      border: none;
      transition: all 0.3s ease;
      margin-bottom: 30px;
    }

    .card:hover {
      box-shadow: var(--hover-shadow);
    }

    .card-header {
      background: transparent;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      padding: 1.5rem;
    }

    .card-body {
      padding: 1.5rem;
    }

    .form-control, .form-select {
      border-radius: 10px;
      padding: 0.75rem 1rem;
      border: 1px solid #e2e8f0;
      font-size: 0.95rem;
    }

    .form-control:focus, .form-select:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
    }

    .form-label {
      font-weight: 500;
      margin-bottom: 0.5rem;
      color: var(--dark-text);
    }

    .btn-primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      border-radius: 10px;
      padding: 0.75rem 1.5rem;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .btn-primary:hover {
      background-color: var(--secondary-color);
      border-color: var(--secondary-color);
      transform: translateY(-2px);
    }

    .table {
      border-collapse: separate;
      border-spacing: 0;
      width: 100%;
    }

    .table th {
      font-weight: 600;
      color: var(--dark-text);
      border-bottom: 2px solid #e2e8f0;
      padding: 1rem;
    }

    .table td {
      padding: 1rem;
      vertical-align: middle;
      border-bottom: 1px solid #e2e8f0;
    }

    .table tbody tr:hover {
      background-color: rgba(67, 97, 238, 0.05);
    }

    .table-box {
      background: white;
      border-radius: 16px;
      box-shadow: var(--card-shadow);
      overflow: hidden;
    }

    .badge {
      padding: 0.5rem 0.75rem;
      border-radius: 8px;
      font-weight: 500;
      font-size: 0.75rem;
    }

    .file-link {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
      transition: all 0.2s ease;
    }

    .file-link:hover {
      color: var(--secondary-color);
      text-decoration: underline;
    }

    @media (max-width: 768px) {
      .content {
        margin-left: 0;
        padding: 15px;
      }
    }
  </style>
</head>
<body>

<?php include_once __DIR__ . '/../../assets/teacher_sidebar.php'; ?>

<div class="content">
  <div class="page-header">
    <h2 class="mb-2"><i class="fas fa-book me-2"></i>Assign Homework</h2>
    <p class="mb-0">Create and manage assignments for <?= !empty($subject_name) ? "<strong>$subject_name</strong>" : "your students" ?></p>

    <?php if (count($teacher_subjects) > 1): ?>
    <div class="mt-3">
      <form action="" method="GET" class="d-flex align-items-center">
        <label class="me-2 text-white">Filter by Subject:</label>
        <select name="subject_id" class="form-select form-select-sm me-2" style="max-width: 200px; background-color: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3);" onchange="this.form.submit()">
          <option value="">All Subjects</option>
          <?php foreach ($teacher_subjects as $subject): ?>
            <option value="<?= $subject['subject_id'] ?>" <?= $subject['subject_id'] == $subject_id ? 'selected' : '' ?>>
              <?= htmlspecialchars($subject['subject_name']) ?>
            </option>
          <?php endforeach; ?>
        </select>
      </form>
    </div>
    <?php endif; ?>
  </div>

  <div class="row">
    <div class="col-lg-5">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Create New Assignment</h5>
        </div>
        <div class="card-body">
          <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
              <i class="fas fa-check-circle me-2"></i> <?= $success_message ?>
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
          <?php endif; ?>

          <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
              <i class="fas fa-exclamation-circle me-2"></i> <?= $error_message ?>
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
          <?php endif; ?>

          <form action="" method="POST" enctype="multipart/form-data">
            <?php if (count($teacher_subjects) > 1): ?>
            <div class="mb-3">
              <label for="subject_id" class="form-label">Subject</label>
              <select name="subject_id" id="subject_id" class="form-select" required>
                <?php foreach ($teacher_subjects as $subject): ?>
                  <option value="<?= $subject['subject_id'] ?>" <?= $subject['subject_id'] == $subject_id ? 'selected' : '' ?>>
                    <?= htmlspecialchars($subject['subject_name']) ?>
                  </option>
                <?php endforeach; ?>
              </select>
            </div>
            <?php else: ?>
              <input type="hidden" name="subject_id" value="<?= $subject_id ?>">
            <?php endif; ?>

            <div class="mb-3">
              <label for="grade" class="form-label">Grade</label>
              <select name="grade" id="grade" class="form-select" required>
                <option value="">Select Grade</option>
                <?php for ($i = 6; $i <= 11; $i++): ?>
                  <option value="Grade <?= $i ?>">Grade <?= $i ?></option>
                <?php endfor; ?>
                <option value="A/L">A/L</option>
              </select>
            </div>

            <div class="mb-3">
              <label class="form-label">Title</label>
              <input type="text" name="title" class="form-control" placeholder="Enter assignment title" required>
            </div>

            <div class="mb-3">
              <label class="form-label">Description</label>
              <textarea name="description" class="form-control" rows="3" placeholder="Provide detailed instructions" required></textarea>
            </div>

            <div class="mb-3">
              <label class="form-label">Deadline</label>
              <input type="date" name="deadline" class="form-control" required>
            </div>

            <div class="mb-4">
              <label class="form-label">Attachment (Optional)</label>
              <input type="file" name="assignment_file" class="form-control">
              <small class="text-muted mt-2 d-block">Max file size: 10MB (PDF, DOC, DOCX)</small>
            </div>

            <button type="submit" class="btn btn-primary w-100">
              <i class="fas fa-paper-plane me-2"></i>Publish Assignment
            </button>
          </form>
        </div>
      </div>
    </div>

    <div class="col-lg-7">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>Your Assignments</h5>
          <span class="badge bg-primary"><?= $assignments->num_rows ?> Total</span>
        </div>
        <div class="card-body p-0">
          <?php if ($assignments->num_rows > 0): ?>
            <div class="table-responsive">
              <table class="table mb-0">
                <thead>
                  <tr>
                    <th>Title</th>
                    <th>Grade</th>
                    <?php if (count($teacher_subjects) > 1 && empty($_GET['subject_id'])): ?>
                    <th>Subject</th>
                    <?php endif; ?>
                    <th>Deadline</th>
                    <th>File</th>
                    <th>Posted</th>
                  </tr>
                </thead>
                <tbody>
                  <?php while ($row = $assignments->fetch_assoc()):
                    // Calculate days remaining
                    $deadline = new DateTime($row['deadline']);
                    $today = new DateTime();
                    $interval = $today->diff($deadline);
                    $daysRemaining = $deadline > $today ? $interval->days : -$interval->days;
                  ?>
                    <tr>
                      <td>
                        <div class="fw-medium"><?= htmlspecialchars($row['title']) ?></div>
                      </td>
                      <td>
                        <span class="badge bg-light text-dark">
                          <?= htmlspecialchars($row['grade']) ?>
                        </span>
                      </td>
                      <?php if (count($teacher_subjects) > 1 && empty($_GET['subject_id'])): ?>
                      <td>
                        <span class="badge bg-primary">
                          <?= htmlspecialchars($row['subject_name']) ?>
                        </span>
                      </td>
                      <?php endif; ?>
                      <td>
                        <div><?= date("d M Y", strtotime($row['deadline'])) ?></div>
                        <?php if ($daysRemaining > 0): ?>
                          <small class="text-success"><?= $daysRemaining ?> days left</small>
                        <?php elseif ($daysRemaining == 0): ?>
                          <small class="text-warning">Due today</small>
                        <?php else: ?>
                          <small class="text-danger">Overdue</small>
                        <?php endif; ?>
                      </td>
                      <td>
                        <?php if ($row['file_path']): ?>
                          <a href="../../<?= $row['file_path'] ?>" target="_blank" class="file-link">
                            <i class="fas fa-file-alt me-1"></i> View
                          </a>
                        <?php else: ?>
                          <span class="text-muted">—</span>
                        <?php endif; ?>
                      </td>
                      <td>
                        <div><?= date("d M Y", strtotime($row['posted_at'])) ?></div>
                        <small class="text-muted"><?= date("h:i A", strtotime($row['posted_at'])) ?></small>
                      </td>
                    </tr>
                  <?php endwhile; ?>
                </tbody>
              </table>
            </div>
          <?php else: ?>
            <div class="text-center py-5">
              <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
              <p>No assignments created yet</p>
              <small class="text-muted">Create your first assignment using the form</small>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
