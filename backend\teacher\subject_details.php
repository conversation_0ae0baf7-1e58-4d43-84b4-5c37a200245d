<?php
session_start();
include '../../backend/server/db_connect.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get teacher_id and subjects
$stmt = $conn->prepare("
    SELECT t.teacher_id, ts.subject_id, s.subject_name
    FROM teachers t
    JOIN teacher_subjects ts ON t.teacher_id = ts.teacher_id
    JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE t.user_id = ?
");

$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_subjects = [];
$teacher_id = null;

while ($row = $result->fetch_assoc()) {
    if (!$teacher_id) {
        $teacher_id = $row['teacher_id'];
    }
    $teacher_subjects[] = [
        'subject_id' => $row['subject_id'],
        'subject_name' => $row['subject_name']
    ];
}
$stmt->close();

// If a specific subject is selected (e.g., from URL parameter)
$subject_id = $_GET['subject_id'] ?? ($teacher_subjects[0]['subject_id'] ?? null);
$subject_name = '';

foreach ($teacher_subjects as $subject) {
    if ($subject['subject_id'] == $subject_id) {
        $subject_name = $subject['subject_name'];
        break;
    }
}

// Handle upload
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $grade = $_POST['grade'];
    $title = $_POST['title'];
    $file_path = null;

    // Get the selected subject_id from the form if multiple subjects
    if (isset($_POST['subject_id']) && !empty($_POST['subject_id'])) {
        $subject_id = $_POST['subject_id'];

        // Update the subject name for display
        foreach ($teacher_subjects as $subject) {
            if ($subject['subject_id'] == $subject_id) {
                $subject_name = $subject['subject_name'];
                break;
            }
        }
    }

    // Validate that the selected subject belongs to this teacher
    $valid_subject = false;
    foreach ($teacher_subjects as $subject) {
        if ($subject['subject_id'] == $subject_id) {
            $valid_subject = true;
            break;
        }
    }

    if (!$valid_subject) {
        $error_message = "Invalid subject selected.";
    } else if (isset($_FILES['material_file']) && $_FILES['material_file']['error'] === 0) {
        $uploadDir = '../../uploads/materials/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        $filename = time() . '_' . basename($_FILES['material_file']['name']);
        $uploadPath = $uploadDir . $filename;
        $file_path = 'uploads/materials/' . $filename;

        if (move_uploaded_file($_FILES['material_file']['tmp_name'], $uploadPath)) {
            $stmt = $conn->prepare("INSERT INTO materials (title, file_path, subject_id, grade, uploaded_at, teacher_id) VALUES (?, ?, ?, ?, NOW(), ?)");
            $stmt->bind_param("ssssi", $title, $file_path, $subject_id, $grade, $teacher_id);

            if ($stmt->execute()) {
                $success_message = "Material uploaded successfully!";
            } else {
                $error_message = "Error uploading material: " . $stmt->error;
            }
            $stmt->close();
        } else {
            $error_message = "Error uploading file. Please try again.";
        }
    } else {
        $error_message = "Please select a valid file to upload.";
    }
}


// Fetch uploaded materials for the selected subject
$stmt = $conn->prepare("SELECT m.*, s.subject_name FROM materials m
                       JOIN subjects s ON m.subject_id = s.subject_id
                       WHERE m.teacher_id = ? AND m.subject_id = ?
                       ORDER BY m.uploaded_at DESC");
$stmt->bind_param("ii", $teacher_id, $subject_id);
$stmt->execute();
$materials = $stmt->get_result();
$stmt->close();
?>
