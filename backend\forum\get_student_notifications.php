<?php
session_start();
// Fix the include path based on how the script is called
$base_path = __DIR__ . '/../../';
include_once $base_path . 'backend/server/db_connect.php';

// Add debugging
error_log("Fetching notifications for student");

header('Content-Type: application/json');

$user_id = $_SESSION['user_id'] ?? 0;
$role = $_SESSION['role'] ?? '';

error_log("User ID: $user_id, Role: $role");

if ($role !== 'student') {
    error_log("Not a student, returning empty array");
    echo json_encode([]);
    exit();
}

// Get student_id
$stmt = $conn->prepare("SELECT student_id FROM students WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();
$student_id = $student['student_id'] ?? 0;
$stmt->close();

error_log("Student ID: $student_id");

$data = [];
if ($student_id) {
    // Check if student_notifications table exists
    $tableExists = false;
    $result = $conn->query("SHOW TABLES LIKE 'student_notifications'");
    if ($result->num_rows > 0) {
        $tableExists = true;
    }

    if (!$tableExists) {
        // If student_notifications table doesn't exist, create it
        $sql = "CREATE TABLE IF NOT EXISTS student_notifications (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            student_id INT(11) NOT NULL,
            message VARCHAR(255) NOT NULL,
            is_read TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            question_id INT(11) NULL
        )";

        if ($conn->query($sql) === TRUE) {
            error_log("Student notifications table created successfully");
        } else {
            error_log("Error creating student notifications table: " . $conn->error);
        }
    }

    // Get unread notifications for this student
    $q = $conn->prepare("
        SELECT id, message, created_at, question_id 
        FROM student_notifications 
        WHERE student_id = ? AND is_read = 0
        ORDER BY created_at DESC
    ");
    $q->bind_param("i", $student_id);
    $q->execute();
    $r = $q->get_result();

    error_log("Found " . $r->num_rows . " unread notifications");

    while ($row = $r->fetch_assoc()) {
        $data[] = $row;
        error_log("Notification: " . $row['message']);
    }

    // If no notifications, check for newly answered questions
    if (count($data) === 0) {
        // Get questions that have been answered but not marked as notified
        $q = $conn->prepare("
            SELECT q.id, q.subject_id, q.question_text, q.reply_text, q.replied_at, s.subject_name
            FROM questions q
            JOIN subjects s ON q.subject_id = s.subject_id
            WHERE q.student_id = ? 
            AND q.reply_text IS NOT NULL 
            AND q.reply_text != '' 
            AND (q.is_notified = 0 OR q.is_notified IS NULL)
            ORDER BY q.replied_at DESC
        ");
        $q->bind_param("i", $student_id);
        $q->execute();
        $questionResult = $q->get_result();

        error_log("Found " . $questionResult->num_rows . " newly answered questions");

        // Create notifications for newly answered questions
        while ($question = $questionResult->fetch_assoc()) {
            $subjectName = $question['subject_name'] ?? 'your subject';
            $message = "Your question in {$subjectName} has been answered";

            // Insert notification
            $insertQuery = $conn->prepare("
                INSERT INTO student_notifications (student_id, message, created_at, question_id)
                VALUES (?, ?, ?, ?)
            ");
            $insertQuery->bind_param("issi", $student_id, $message, $question['replied_at'], $question['id']);

            if ($insertQuery->execute()) {
                $notificationId = $conn->insert_id;
                $data[] = [
                    'id' => $notificationId,
                    'message' => $message,
                    'created_at' => $question['replied_at'],
                    'question_id' => $question['id']
                ];
                error_log("Created notification: $message");

                // Mark question as notified
                $updateQuery = $conn->prepare("UPDATE questions SET is_notified = 1 WHERE id = ?");
                $updateQuery->bind_param("i", $question['id']);
                $updateQuery->execute();
            }
        }
    }
}

echo json_encode($data);
?>
