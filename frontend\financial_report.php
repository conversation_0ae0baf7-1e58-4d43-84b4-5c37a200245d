<?php
session_start();
include_once '../backend/server/db_connect.php';

// Check if user is logged in and is an owner
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'owner') {
    header("Location: ../backend/server/login.php");
    exit();
}

// Get all subjects and grades for filters
$subjects_query = "SELECT DISTINCT subject_id, subject_name FROM subjects ORDER BY subject_name";
$subjects_result = $conn->query($subjects_query);

$grades_query = "SELECT DISTINCT grade FROM students ORDER BY grade";
$grades_result = $conn->query($grades_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Financial Reports - Gurukula Institution</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome for icons -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <!-- Google Fonts - Poppins -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <!-- Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: 'Poppins', sans-serif;
      background-color: #f8f9fc;
    }
    
    .content {
      margin-left: 260px;
      padding: 30px;
      transition: all 0.3s;
    }
    
    .page-header {
      margin-bottom: 24px;
      border-bottom: 1px solid #e3e6f0;
      padding-bottom: 20px;
    }
    
    .page-header h2 {
      color: #5a5c69;
      font-weight: 700;
      margin-bottom: 8px;
    }
    
    .card {
      border: none;
      border-radius: 0.75rem;
      box-shadow: 0 0.15rem 1.75rem rgba(0, 0, 0, 0.1);
      margin-bottom: 24px;
      overflow: hidden;
    }
    
    .card-header {
      background-color: #f8f9fc;
      border-bottom: 1px solid #e3e6f0;
      padding: 1rem 1.25rem;
    }
    
    .card-header h4, .card-header h5 {
      margin: 0;
      color: #5a5c69;
      font-weight: 700;
    }
    
    .filters {
      background-color: #fff;
      padding: 20px;
      border-radius: 0.75rem;
      margin-bottom: 24px;
      box-shadow: 0 0.15rem 1.75rem rgba(0, 0, 0, 0.1);
    }
    
    .table th {
      background-color: #f8f9fc;
      color: #5a5c69;
      font-weight: 700;
      border-bottom-width: 1px;
    }
    
    .table-striped tbody tr:nth-of-type(odd) {
      background-color: rgba(0, 0, 0, 0.02);
    }
    
    .table-hover tbody tr:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    .status-paid {
      color: var(--success-color);
      font-weight: bold;
      display: inline-block;
      padding: 0.25rem 0.5rem;
      background-color: rgba(28, 200, 138, 0.1);
      border-radius: 0.25rem;
    }
    
    .status-not-paid {
      color: var(--danger-color);
      font-weight: bold;
      display: inline-block;
      padding: 0.25rem 0.5rem;
      background-color: rgba(231, 74, 59, 0.1);
      border-radius: 0.25rem;
    }
    
    .form-select:focus, .form-control:focus {
      border-color: #bac8f3;
      box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
    }
    
    .btn-primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }
    
    .btn-primary:hover {
      background-color: #2e59d9;
      border-color: #2e59d9;
    }
    
    .btn-success {
      background-color: var(--success-color);
      border-color: var(--success-color);
    }
    
    .btn-success:hover {
      background-color: #17a673;
      border-color: #17a673;
    }
    
    .summary-stat {
      text-align: center;
      padding: 1rem;
    }
    
    .summary-stat h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }
    
    .summary-stat p {
      color: #858796;
      margin-bottom: 0;
      font-weight: 600;
    }
  </style>
</head>
<body>

<?php include_once '../assets/owner_sidebar.php'; ?>

<div class="content">
  <div class="page-header d-flex justify-content-between align-items-center">
    <div>
      <h2><i class="fas fa-chart-pie me-2"></i>Financial Report</h2>
      <p class="text-body-secondary">Comprehensive overview of payment status and revenue</p>
    </div>
    <button id="downloadReportBtn" class="btn btn-success">
      <i class="fas fa-download me-2"></i> Download Report
    </button>
  </div>
  
  <!-- Overview Card -->
  <div class="card">
    <div class="card-header bg-white">
      <h4 class="text-primary"><i class="fas fa-money-bill-wave me-2"></i>Payment Overview</h4>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-4">
          <canvas id="paymentChart" height="200"></canvas>
        </div>
        <div class="col-md-4">
          <div id="summaryStats" class="d-flex flex-column justify-content-center h-100">
            <!-- Stats will be loaded here -->
          </div>
        </div>
        <div class="col-md-4">
          <canvas id="subjectPaymentChart" height="200"></canvas>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Filters -->
  <div class="filters">
    <h5 class="mb-3 text-primary"><i class="fas fa-filter me-2"></i>Filter Options</h5>
    <div class="row">
      <div class="col-md-3">
        <label for="subjectFilter" class="form-label">Subject</label>
        <select id="subjectFilter" class="form-select form-select-sm">
          <option value="">All Subjects</option>
          <?php while($subject = $subjects_result->fetch_assoc()): ?>
            <option value="<?= $subject['subject_id'] ?>"><?= htmlspecialchars($subject['subject_name']) ?></option>
          <?php endwhile; ?>
        </select>
      </div>
      <div class="col-md-3">
        <label for="gradeFilter" class="form-label">Grade</label>
        <select id="gradeFilter" class="form-select form-select-sm">
          <option value="">All Grades</option>
          <?php while($grade = $grades_result->fetch_assoc()): ?>
            <option value="<?= $grade['grade'] ?>">Grade <?= htmlspecialchars($grade['grade']) ?></option>
          <?php endwhile; ?>
        </select>
      </div>
      <div class="col-md-3">
        <label for="statusFilter" class="form-label">Payment Status</label>
        <select id="statusFilter" class="form-select form-select-sm">
          <option value="">All Statuses</option>
          <option value="Paid">Paid</option>
          <option value="Not Paid">Not Paid</option>
        </select>
      </div>
      <div class="col-md-3 d-flex align-items-end">
        <button id="applyFilters" class="btn btn-primary btn-sm me-2">
          <i class="fas fa-search me-1"></i> Apply Filters
        </button>
        <button id="clearFilters" class="btn btn-secondary btn-sm">
          <i class="fas fa-undo me-1"></i> Clear
        </button>
      </div>
    </div>
  </div>
  
  <!-- Student Payment Details -->
  <div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
      <h5 class="text-primary"><i class="fas fa-list me-2"></i>Student Payment Details</h5>
      <span id="recordCount" class="badge bg-primary">0 Records</span>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table id="paymentsTable" class="table table-striped table-hover">
          <thead>
            <tr>
              <th>Student ID</th>
              <th>Student Name</th>
              <th>Grade</th>
              <th>Subject</th>
              <th>Amount</th>
              <th>Status</th>
              <th>Last Payment Date</th>
            </tr>
          </thead>
          <tbody id="paymentsTableBody">
            <!-- Data will be loaded here -->
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
  // Load chart data
  async function loadChartData() {
    try {
      const res = await fetch('../backend/fetch_financial_data.php');
      const data = await res.json();
      
      // Create the chart
      const ctx = document.getElementById('paymentChart').getContext('2d');
      new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['Paid', 'Not Paid'],
          datasets: [{
            label: 'Students',
            data: [data['Paid'], data['Not Paid']],
            backgroundColor: ['#1cc88a', '#e74a3b'],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom'
            },
            title: {
              display: true,
              text: 'Payment Status Distribution'
            }
          }
        }
      });
      
      // Update summary stats
      const total = data['Paid'] + data['Not Paid'];
      const paidPercentage = total > 0 ? Math.round((data['Paid'] / total) * 100) : 0;
      
      document.getElementById('summaryStats').innerHTML = `
        <div class="summary-stat">
          <h2 class="text-primary">${paidPercentage}%</h2>
          <p>Payment Rate</p>
        </div>
        <div class="d-flex justify-content-around">
          <div class="text-center">
            <h4 class="text-success">${data['Paid']}</h4>
            <p>Paid Students</p>
          </div>
          <div class="text-center">
            <h4 class="text-danger">${data['Not Paid']}</h4>
            <p>Unpaid Students</p>
          </div>
        </div>
      `;
    } catch (error) {
      console.error('Error loading chart data:', error);
    }
  }
  
  // Load student payment details
  async function loadPaymentDetails() {
    try {
      const subject = $('#subjectFilter').val();
      const grade = $('#gradeFilter').val();
      const status = $('#statusFilter').val();
      
      console.log("Fetching payment details with filters:", { subject, grade, status });
      
      const res = await fetch(`../backend/fetch_student_payments.php?subject=${subject}&grade=${grade}&status=${status}`);
      
      if (!res.ok) {
        throw new Error(`HTTP error! Status: ${res.status}`);
      }
      
      const data = await res.json();
      console.log("Payment data received:", data);
      
      // Check if there's an error in the response
      if (data.error) {
        console.error("Error from server:", data.error);
        $('#paymentsTableBody').html('<tr><td colspan="7" class="text-center text-danger">Error: ' + data.error + '</td></tr>');
        return;
      }
      
      let tableHtml = '';
      
      if (!Array.isArray(data) || data.length === 0) {
        tableHtml = '<tr><td colspan="7" class="text-center">No records found</td></tr>';
        $('#recordCount').text('0 Records');
      } else {
        data.forEach(student => {
          const statusClass = student.status === 'Paid' ? 'status-paid' : 'status-not-paid';
          const paymentDate = student.payment_date ? new Date(student.payment_date).toLocaleDateString() : '-';
          
          tableHtml += `
            <tr>
              <td>${student.student_id}</td>
              <td>${student.name}</td>
              <td>Grade ${student.grade}</td>
              <td>${student.subject_name || 'N/A'}</td>
              <td>Rs. ${student.amount || 0}</td>
              <td><span class="${statusClass}">${student.status}</span></td>
              <td>${paymentDate}</td>
            </tr>
          `;
        });
        
        $('#recordCount').text(`${data.length} Records`);
      }
      
      $('#paymentsTableBody').html(tableHtml);
    } catch (error) {
      console.error('Error loading payment details:', error);
      $('#paymentsTableBody').html('<tr><td colspan="7" class="text-center text-danger">Error loading data: ' + error.message + '</td></tr>');
    }
  }

  // Load subject-wise payment data
  async function loadSubjectPaymentData() {
    try {
      const res = await fetch('../backend/fetch_subject_payment_data.php');
      const data = await res.json();
      
      if (data.error) {
        console.error("Error from server:", data.error);
        return;
      }
      
      // Create the subject payment chart
      const ctx = document.getElementById('subjectPaymentChart').getContext('2d');
      new Chart(ctx, {
        type: 'bar',
        data: {
          labels: data.subjects,
          datasets: [{
            label: 'Payment Rate (%)',
            data: data.rates,
            backgroundColor: data.colors,
            borderColor: data.colors.map(color => color.replace('0.7', '1')),
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: 'Subject-wise Payment Rate'
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return context.raw + '% paid';
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: 'Payment Rate (%)'
              },
              ticks: {
                callback: function(value) {
                  return value + '%';
                }
              }
            }
          }
        }
      });
    } catch (error) {
      console.error('Error loading subject payment data:', error);
    }
  }

  // Event listeners
  $(document).ready(function() {
    loadChartData();
    loadPaymentDetails();
    loadSubjectPaymentData();
    
    $('#applyFilters').click(function() {
      loadPaymentDetails();
    });
    
    $('#clearFilters').click(function() {
      $('#subjectFilter').val('');
      $('#gradeFilter').val('');
      $('#statusFilter').val('');
      loadPaymentDetails();
    });

    // Handle download report button click
    $('#downloadReportBtn').click(function() {
      window.location.href = '../backend/generate_financial_report_pdf.php';
    });
  });
</script>
</body>
</html>
