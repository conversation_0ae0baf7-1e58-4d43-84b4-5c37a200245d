<?php
include '../server/db_connect.php';
session_start();

if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_FILES['profile_image'])) {
    $user_id = $_POST['user_id'];
    $file = $_FILES['profile_image'];

    $uploadDir = '../../uploads/profiles/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    $ext = pathinfo($file['name'], PATHINFO_EXTENSION);
    $newName = 'teacher_' . $user_id . '_' . time() . '.' . $ext;
    $uploadPath = $uploadDir . $newName;
    $relativePath = 'uploads/profiles/' . $newName; // Store relative path without the '../../'

    if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
        $stmt = $conn->prepare("UPDATE teachers SET profile_image = ? WHERE user_id = ?");
        $stmt->bind_param("si", $relativePath, $user_id);

        if ($stmt->execute()) {
            $_SESSION['upload_success'] = true;
        } else {
            $_SESSION['upload_error'] = "Failed to update profile image in database.";
        }

        $stmt->close();
    } else {
        $_SESSION['upload_error'] = "Failed to upload image. Please try again.";
    }
}

// Check if the request came from the dashboard or the profile page
$referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';

if (strpos($referer, 'teacher_dashboard.php') !== false) {
    header("Location: ../../frontend/dashboards/teacher_dashboard.php");
} else {
    header("Location: ../../frontend/teacher/teacher_detail.php");
}
exit();
