<?php
$currentPage = basename($_SERVER['PHP_SELF']); // e.g. qr_scanner.php
?>

<!-- Mobile menu toggle button -->
<button class="mobile-menu-toggle d-md-none">
  <i class="fas fa-bars"></i>
</button>

<div class="sidebar">
    <h3><i class="fa-solid fa-bars"></i> <span>Gurukula</span></h3>
    <a href="../dashboards/worker_dashboard.php" class="<?= $currentPage === 'worker_dashboard.php' ? 'active' : '' ?>">
      <i class="fas fa-home"></i> <span>Dashboard</span>
    </a>
    <a href="../worker/qr_scanner.php" class="<?= $currentPage === 'qr_scanner.php' ? 'active' : '' ?>">
      <i class="fa-solid fa-qrcode"></i> <span>Attendance Scanner</span>
    </a>
    <a href="../worker/monthly_attendance.php" class="<?= $currentPage === 'monthly_attendance.php' ? 'active' : '' ?>">
      <i class="fa-solid fa-calendar-check"></i> <span>Monthly Attendance</span>
    </a>
    <a href="../worker/monthly_payments.php" class="<?= $currentPage === 'monthly_payments.php' ? 'active' : '' ?>">
      <i class="fa-solid fa-money-check-dollar"></i> <span>Payment Details</span>
    </a>
    <a href="../home.php" class="text-danger">
      <i class="fas fa-sign-out-alt"></i> <span>Logout</span>
    </a>
</div>

<!-- Include responsive CSS and JS -->
<link rel="stylesheet" href="../../assets/css/responsive.css">
<script src="../../assets/js/responsive-nav.js" defer></script>

<style>
  /* Sidebar styles */
  .sidebar {
    height: 100vh;
    width: 260px;
    position: fixed;
    top: 0;
    left: 0;
    background-color: #02005F;
    padding: 25px;
    color: #fff;
    transition: all 0.3s;
    z-index: 1040;
  }
  .sidebar h3 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 1.8rem;
  }
  .sidebar a {
    color: #fff;
    text-decoration: none;
    display: block;
    padding: 12px;
    margin-bottom: 12px;
    border-radius: 8px;
    font-size: 1.2rem;
    transition: 0.3s;
  }
  .sidebar a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
  }
  .sidebar a:hover,
  .sidebar a.active {
    background-color: #67BAFD;
    transform: scale(1.05);
  }
</style>
