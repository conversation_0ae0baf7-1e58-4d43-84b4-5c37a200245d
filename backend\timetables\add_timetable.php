<?php
session_start();
include_once '../../backend/server/db_connect.php'; // Adjust path if needed

// Ensure user is logged in as a teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get teacher_id and subjects
$stmt = $conn->prepare("
    SELECT t.teacher_id, ts.subject_id, s.subject_name 
    FROM teachers t 
    JOIN teacher_subjects ts ON t.teacher_id = ts.teacher_id
    JOIN subjects s ON ts.subject_id = s.subject_id 
    WHERE t.user_id = ?
");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_subjects = [];
$teacher_id = null;

while ($row = $result->fetch_assoc()) {
    if (!$teacher_id) {
        $teacher_id = $row['teacher_id'];
    }
    $teacher_subjects[] = [
        'subject_id' => $row['subject_id'],
        'subject_name' => $row['subject_name']
    ];
}
$stmt->close();
?>
