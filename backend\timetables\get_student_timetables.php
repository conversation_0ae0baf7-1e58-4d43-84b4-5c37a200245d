<?php
session_start();
include_once '../../backend/server/db_connect.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log the request
error_log("get_student_timetables.php called by user ID: " . ($_SESSION['user_id'] ?? 'not logged in'));

header('Content-Type: application/json');

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    // Get student_id and grade
    $stmt = $conn->prepare("SELECT student_id, grade FROM students WHERE user_id = ?");
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $stmt->bind_param("i", $user_id);
    if (!$stmt->execute()) {
        throw new Exception("Execute failed: " . $stmt->error);
    }
    
    $result = $stmt->get_result();
    if ($result->num_rows === 0) {
        throw new Exception("No student record found for this user");
    }
    
    $student = $result->fetch_assoc();
    $student_id = $student['student_id'];
    $grade = $student['grade'];
    
    error_log("Student ID: $student_id, Grade: $grade");
    
    // Get all timetables for this student's grade
    $timetable_query = $conn->prepare("
        SELECT tt.day_of_week as day, tt.grade, s.subject_name as subject, 
               CONCAT(u.first_name, ' ', u.last_name) as teacher_name,
               CONCAT(DATE_FORMAT(tt.start_time, '%h:%i %p'), ' - ', DATE_FORMAT(tt.end_time, '%h:%i %p')) as time
        FROM teacher_timetable tt
        JOIN subjects s ON tt.subject_id = s.subject_id
        JOIN teachers t ON tt.teacher_id = t.teacher_id
        JOIN users u ON t.user_id = u.id
        WHERE tt.grade = ?
        ORDER BY FIELD(tt.day_of_week, 'Monday','Tuesday','Wednesday','Thursday','Friday','Saturday','Sunday'), tt.start_time
    ");
    
    if (!$timetable_query) {
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $timetable_query->bind_param("s", $grade);
    if (!$timetable_query->execute()) {
        throw new Exception("Execute failed: " . $timetable_query->error);
    }
    
    $timetable_result = $timetable_query->get_result();
    
    $timetables = [];
    while ($row = $timetable_result->fetch_assoc()) {
        $timetables[] = $row;
    }
    
    error_log("Found " . count($timetables) . " timetable entries for grade $grade");
    
    echo json_encode($timetables);
    
} catch (Exception $e) {
    error_log("Error in get_student_timetables.php: " . $e->getMessage());
    echo json_encode(['error' => $e->getMessage()]);
}

$conn->close();
?>
