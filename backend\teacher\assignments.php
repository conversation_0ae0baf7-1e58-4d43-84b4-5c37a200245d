<?php
session_start();
include '../../backend/server/db_connect.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get teacher ID and subjects
$stmt = $conn->prepare("
    SELECT t.teacher_id, ts.subject_id, s.subject_name
    FROM teachers t
    JOIN teacher_subjects ts ON t.teacher_id = ts.teacher_id
    JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE t.user_id = ?
");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_subjects = [];
$teacher_id = null;

while ($row = $result->fetch_assoc()) {
    if (!$teacher_id) {
        $teacher_id = $row['teacher_id'];
    }
    $teacher_subjects[] = [
        'subject_id' => $row['subject_id'],
        'subject_name' => $row['subject_name']
    ];
}
$stmt->close();

// If a specific subject is selected (e.g., from URL parameter)
$subject_id = $_GET['subject_id'] ?? ($teacher_subjects[0]['subject_id'] ?? null);
$subject_name = '';

foreach ($teacher_subjects as $subject) {
    if ($subject['subject_id'] == $subject_id) {
        $subject_name = $subject['subject_name'];
        break;
    }
}

// Upload assignment
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $grade = $_POST['grade'];
    $title = $_POST['title'];
    $description = $_POST['description'];
    $deadline = $_POST['deadline'];
    $file_path = null;

    // Get the selected subject_id from the form if multiple subjects
    if (isset($_POST['subject_id']) && !empty($_POST['subject_id'])) {
        $subject_id = $_POST['subject_id'];

        // Update the subject name for display
        foreach ($teacher_subjects as $subject) {
            if ($subject['subject_id'] == $subject_id) {
                $subject_name = $subject['subject_name'];
                break;
            }
        }
    }

    // Validate that the selected subject belongs to this teacher
    $valid_subject = false;
    foreach ($teacher_subjects as $subject) {
        if ($subject['subject_id'] == $subject_id) {
            $valid_subject = true;
            break;
        }
    }

    if (!$valid_subject) {
        $error_message = "Invalid subject selected.";
    } else {
        // Process file upload if present
        if (isset($_FILES['assignment_file']) && $_FILES['assignment_file']['error'] === 0) {
            $uploadDir = '../../uploads/assignments/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            $filename = time() . '_' . basename($_FILES['assignment_file']['name']);
            $uploadPath = $uploadDir . $filename;
            $file_path = 'uploads/assignments/' . $filename;

            if (move_uploaded_file($_FILES['assignment_file']['tmp_name'], $uploadPath)) {
                // File uploaded successfully
            } else {
                $error_message = "Error uploading file. Please try again.";
                $file_path = null;
            }
        }

        if (!isset($error_message)) {
            try {
                // Try to insert the assignment with subject_id
                $stmt = $conn->prepare("INSERT INTO assignments (teacher_id, subject_id, grade, title, description, file_path, deadline) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $stmt->bind_param("iisssss", $teacher_id, $subject_id, $grade, $title, $description, $file_path, $deadline);

                if ($stmt->execute()) {
                    $success_message = "Assignment created successfully!";
                } else {
                    $error_message = "Error creating assignment: " . $stmt->error;
                }
                $stmt->close();
            } catch (mysqli_sql_exception $e) {
                // Fallback if subject_id column doesn't exist
                $stmt = $conn->prepare("INSERT INTO assignments (teacher_id, grade, title, description, file_path, deadline) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->bind_param("isssss", $teacher_id, $grade, $title, $description, $file_path, $deadline);

                if ($stmt->execute()) {
                    $success_message = "Assignment created successfully! Note: Subject association may not be saved due to database structure.";
                } else {
                    $error_message = "Error creating assignment: " . $stmt->error;
                }
                $stmt->close();
            }
        }
    }
}

// Fetch assignments for the selected subject if specified, otherwise fetch all
try {
    if (isset($_GET['subject_id']) && !empty($_GET['subject_id'])) {
        $stmt = $conn->prepare("
            SELECT a.*, IFNULL(s.subject_name, 'Unassigned') as subject_name
            FROM assignments a
            LEFT JOIN subjects s ON a.subject_id = s.subject_id
            WHERE a.teacher_id = ? AND (a.subject_id = ? OR a.subject_id IS NULL)
            ORDER BY a.posted_at DESC
        ");
        $stmt->bind_param("ii", $teacher_id, $subject_id);
    } else {
        $stmt = $conn->prepare("
            SELECT a.*, IFNULL(s.subject_name, 'Unassigned') as subject_name
            FROM assignments a
            LEFT JOIN subjects s ON a.subject_id = s.subject_id
            WHERE a.teacher_id = ?
            ORDER BY a.posted_at DESC
        ");
        $stmt->bind_param("i", $teacher_id);
    }
} catch (mysqli_sql_exception $e) {
    // Fallback query if subject_id column doesn't exist
    $stmt = $conn->prepare("
        SELECT a.*, 'Unassigned' as subject_name
        FROM assignments a
        WHERE a.teacher_id = ?
        ORDER BY a.posted_at DESC
    ");
    $stmt->bind_param("i", $teacher_id);
}
$stmt->execute();
$assignments = $stmt->get_result();
$stmt->close();
?>
