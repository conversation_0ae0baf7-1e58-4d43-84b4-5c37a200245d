<?php
// Include database connection
include_once 'backend/server/db_connect.php';

// Create subject_fees table
$create_table_sql = "
CREATE TABLE IF NOT EXISTS subject_fees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_id INT NOT NULL,
    grade VARCHAR(20) NOT NULL,
    fee DECIMAL(10,2) NOT NULL DEFAULT 500.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(subject_id) ON DELETE CASCADE,
    UNIQUE KEY subject_grade_unique (subject_id, grade)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

if ($conn->query($create_table_sql)) {
    echo "<h2>subject_fees table created successfully!</h2>";
} else {
    echo "<h2>Error creating subject_fees table: " . $conn->error . "</h2>";
}

// Check if there are any existing subjects with fees
$result = $conn->query("SELECT subject_id, fee FROM subjects WHERE fee IS NOT NULL");

if ($result->num_rows > 0) {
    echo "<h3>Migrating existing fees to the new table...</h3>";
    
    // Get all possible grades
    $grades = ["Grade 6", "Grade 7", "Grade 8", "Grade 9", "Grade 10", "Grade 11", "Grade 12", "Grade 13", "A/L"];
    
    // For each subject, create entries for all grades with the same fee
    while ($row = $result->fetch_assoc()) {
        $subject_id = $row['subject_id'];
        $fee = $row['fee'];
        
        foreach ($grades as $grade) {
            // Check if this combination already exists
            $check = $conn->prepare("SELECT id FROM subject_fees WHERE subject_id = ? AND grade = ?");
            $check->bind_param("is", $subject_id, $grade);
            $check->execute();
            $check_result = $check->get_result();
            
            if ($check_result->num_rows == 0) {
                // Insert new fee record
                $insert = $conn->prepare("INSERT INTO subject_fees (subject_id, grade, fee) VALUES (?, ?, ?)");
                $insert->bind_param("isd", $subject_id, $grade, $fee);
                
                if ($insert->execute()) {
                    echo "Added fee for subject ID $subject_id, grade $grade: $fee<br>";
                } else {
                    echo "Error adding fee for subject ID $subject_id, grade $grade: " . $insert->error . "<br>";
                }
                $insert->close();
            }
            $check->close();
        }
    }
}

$conn->close();
echo "<p><a href='frontend/teacher/manage_fees.php'>Go to Manage Fees page</a></p>";
?>
