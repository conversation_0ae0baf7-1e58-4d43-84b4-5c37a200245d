<?php
require_once '../server/db_connect.php';
session_start();

$user_role = $_SESSION['role'];
$user_id = $_SESSION['user_id'];

if ($user_role === 'teacher') {
    // Teachers see all related questions
    $stmt = $conn->prepare("SELECT * FROM questions WHERE subject_id IN (SELECT subject_id FROM teacher_subjects WHERE teacher_id = ?)");
    $stmt->bind_param("i", $user_id);
} else {
    // Students see only their questions
    $stmt = $conn->prepare("SELECT * FROM questions WHERE student_id = ?");
    $stmt->bind_param("i", $user_id);
}

$stmt->execute();
$result = $stmt->get_result();
$questions = $result->fetch_all(MYSQLI_ASSOC);

echo json_encode($questions);
