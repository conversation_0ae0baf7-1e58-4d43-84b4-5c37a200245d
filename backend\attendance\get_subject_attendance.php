<?php
// Include database connection
include_once '../../backend/server/db_connect.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Get current month for filtering
$current_month = date('m');
$current_year = date('Y');

// Query to get attendance count by subject
$query = "
    SELECT 
        a.subject_name,
        COUNT(DISTINCT a.student_id) as student_count
    FROM 
        attendance a
    WHERE 
        MONTH(a.date) = ? AND YEAR(a.date) = ?
        AND a.subject_name IS NOT NULL
    GROUP BY 
        a.subject_name
    ORDER BY 
        student_count DESC
";

$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $current_month, $current_year);
$stmt->execute();
$result = $stmt->get_result();

// Prepare data for chart
$subjects = [];
$counts = [];

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $subjects[] = $row['subject_name'];
        $counts[] = (int)$row['student_count'];
    }
}

// If no data, provide some sample data
if (empty($subjects)) {
    // Query to get all subjects even if no attendance
    $all_subjects_query = "SELECT subject_name FROM subjects ORDER BY subject_name";
    $all_subjects_result = $conn->query($all_subjects_query);
    
    if ($all_subjects_result->num_rows > 0) {
        while ($row = $all_subjects_result->fetch_assoc()) {
            $subjects[] = $row['subject_name'];
            $counts[] = 0;
        }
    } else {
        $subjects = ['Mathematics', 'Science', 'English', 'History', 'Art'];
        $counts = [0, 0, 0, 0, 0];
    }
}

// Return JSON response
echo json_encode([
    'subjects' => $subjects,
    'counts' => $counts
]);

$stmt->close();
$conn->close();