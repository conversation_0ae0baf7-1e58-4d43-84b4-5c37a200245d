<?php
include_once 'server/db_connect.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $subject_name = trim($_POST['subject_name']);

    echo '<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">';
    echo '<div class="container mt-4">';

    // Validate input
    if (empty($subject_name)) {
        echo '<div class="alert alert-danger" role="alert">Subject name is required.</div>';
        echo '</div>';
        exit;
    }

    // Insert into DB using prepared statement
    $stmt = $conn->prepare("INSERT INTO subjects (subject_name) VALUES (?)");
    $stmt->bind_param("s", $subject_name);

    if ($stmt->execute()) {
        echo '<div class="alert alert-success" role="alert">Subject added successfully.</div>';
    } else {
        echo '<div class="alert alert-danger" role="alert">Error: ' . htmlspecialchars($stmt->error) . '</div>';
    }

    echo '</div>';
    $stmt->close();
    $conn->close();
}
?>


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Add Subject</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">

<div class="container mt-5">
    <div class="card mx-auto shadow" style="max-width: 500px;">
        <div class="card-body">
            <h3 class="card-title mb-4 text-center">Add New Subject</h3>

            <form action="add_subject.php" method="POST">
                <div class="mb-3">
                    <label for="subject_name" class="form-label">Subject Name</label>
                    <input type="text" class="form-control" id="subject_name" name="subject_name" placeholder="Enter subject name" required>
                </div>

                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-success">Add Subject</button>
                    <a href="../frontend/dashboards/owner_dashboard.php" class="btn btn-secondary">Go to Dashboard</a>
                </div>
            </form>
        </div>
    </div>
</div>

</body>
</html>
