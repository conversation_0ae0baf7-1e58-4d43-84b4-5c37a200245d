<?php
echo "<h1>PHP GD Library Check</h1>";

// Check if GD is available
if (extension_loaded('gd')) {
    echo "<p style='color:green; font-weight:bold;'>✓ GD library is loaded!</p>";
    
    // Get GD info
    $gd_info = gd_info();
    echo "<h2>GD Information:</h2>";
    echo "<pre>";
    print_r($gd_info);
    echo "</pre>";
    
    // Test image creation
    echo "<h2>Testing Image Creation:</h2>";
    if (function_exists('imagecreate')) {
        echo "<p style='color:green; font-weight:bold;'>✓ imagecreate() function exists</p>";
        
        // Try to create a simple image
        try {
            $image = imagecreate(100, 100);
            $background = imagecolorallocate($image, 255, 255, 255);
            $text_color = imagecolorallocate($image, 0, 0, 0);
            imagestring($image, 5, 10, 40, "GD works!", $text_color);
            
            // Output image to browser
            ob_start();
            imagepng($image);
            $image_data = ob_get_clean();
            imagedestroy($image);
            
            echo "<p style='color:green; font-weight:bold;'>✓ Successfully created test image:</p>";
            echo '<img src="data:image/png;base64,' . base64_encode($image_data) . '" alt="Test Image">';
        } catch (Exception $e) {
            echo "<p style='color:red; font-weight:bold;'>✗ Error creating image: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color:red; font-weight:bold;'>✗ imagecreate() function does not exist!</p>";
    }
} else {
    echo "<p style='color:red; font-weight:bold;'>✗ GD library is NOT loaded!</p>";
    
    // Check php.ini location
    echo "<h2>PHP Configuration:</h2>";
    echo "<p>Loaded php.ini: " . php_ini_loaded_file() . "</p>";
    
    // Check if extension=gd is in php.ini
    $php_ini = php_ini_loaded_file();
    if (file_exists($php_ini)) {
        $ini_content = file_get_contents($php_ini);
        if (strpos($ini_content, 'extension=gd') !== false) {
            if (strpos($ini_content, ';extension=gd') !== false) {
                echo "<p style='color:red; font-weight:bold;'>✗ GD is commented out in php.ini (;extension=gd)</p>";
            } else {
                echo "<p style='color:orange; font-weight:bold;'>⚠ GD is enabled in php.ini but not loading!</p>";
            }
        } else {
            echo "<p style='color:red; font-weight:bold;'>✗ GD extension not found in php.ini</p>";
        }
    }
}

// Check for other PHP extensions
echo "<h2>Loaded Extensions:</h2>";
$extensions = get_loaded_extensions();
sort($extensions);
echo "<ul>";
foreach ($extensions as $ext) {
    echo "<li>$ext</li>";
}
echo "</ul>";

// PHP version info
echo "<h2>PHP Version:</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
?>

<h2>Manual Fix Instructions:</h2>
<ol>
    <li>Stop Apache in XAMPP Control Panel</li>
    <li>Open <code><?php echo php_ini_loaded_file(); ?></code> in a text editor (run as administrator)</li>
    <li>Find the line <code>;extension=gd</code></li>
    <li>Remove the semicolon (;) so it becomes <code>extension=gd</code></li>
    <li>Save the file</li>
    <li>Start Apache in XAMPP Control Panel</li>
    <li>Refresh this page</li>
</ol>
