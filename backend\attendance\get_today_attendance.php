<?php
// Include database connection
include_once '../../backend/server/db_connect.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Get today's date
$today = date('Y-m-d');

// Query to get today's attendance count
$query = "SELECT COUNT(DISTINCT student_id) as total FROM attendance WHERE date = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $today);
$stmt->execute();
$result = $stmt->get_result();

// Initialize response
$response = ['count' => 0];

// Get count
if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $response['count'] = (int)$row['total'];
}

// Return JSON response
echo json_encode($response);

// Close connections
$stmt->close();
$conn->close();
