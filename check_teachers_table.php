<?php
// Include database connection
include_once 'backend/server/db_connect.php';

// Check teachers table structure
echo "<h2>Teachers Table Structure</h2>";
$result = $conn->query("DESCRIBE teachers");
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "<td>" . $row['Extra'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Get a sample row from teachers table
$result = $conn->query("SELECT * FROM teachers LIMIT 1");
if ($result->num_rows > 0) {
    echo "<h2>Sample Teacher Record</h2>";
    echo "<table border='1'>";
    $row = $result->fetch_assoc();
    echo "<tr>";
    foreach ($row as $key => $value) {
        echo "<th>" . $key . "</th>";
    }
    echo "</tr>";
    echo "<tr>";
    foreach ($row as $value) {
        echo "<td>" . $value . "</td>";
    }
    echo "</tr>";
    echo "</table>";
} else {
    echo "<p>No records found in teachers table.</p>";
}

$conn->close();
?>
