<?php include_once __DIR__ . '/../../backend/teacher/subject_details.php'; ?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Subject Details - Gurukula</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-bg: #f4f6f9;
      --dark-text: #2d3748;
      --light-text: #718096;
      --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    body {
      background-color: var(--light-bg);
      font-family: 'Poppins', sans-serif;
      color: var(--dark-text);
    }

    .content {
      margin-left: 280px;
      padding: 30px;
    }

    .page-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
    }

    .page-header::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 200%;
      background: rgba(255, 255, 255, 0.1);
      transform: rotate(30deg);
    }

    .card {
      border: none;
      border-radius: 16px;
      box-shadow: var(--card-shadow);
      transition: all 0.3s ease;
      margin-bottom: 30px;
      overflow: hidden;
    }

    .card:hover {
      transform: translateY(-5px);
      box-shadow: var(--hover-shadow);
    }

    .card-header {
      background-color: #fff;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      padding: 1.5rem;
      font-weight: 600;
    }

    .card-body {
      padding: 1.5rem;
    }

    .form-control, .form-select {
      border-radius: 10px;
      padding: 0.75rem 1rem;
      border: 1px solid #e2e8f0;
      transition: all 0.3s;
    }

    .form-control:focus, .form-select:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
    }

    .form-label {
      font-weight: 500;
      margin-bottom: 0.5rem;
      color: var(--dark-text);
    }

    .btn-primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      border-radius: 10px;
      padding: 0.75rem 1.5rem;
      font-weight: 500;
      transition: all 0.3s;
    }

    .btn-primary:hover {
      background-color: var(--secondary-color);
      border-color: var(--secondary-color);
      transform: translateY(-2px);
    }

    .table {
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 0 10px rgba(0,0,0,0.05);
    }

    .table thead th {
      background-color: #f8fafc;
      border-bottom: none;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.8rem;
      letter-spacing: 0.5px;
      padding: 1rem;
    }

    .table tbody td {
      padding: 1rem;
      vertical-align: middle;
    }

    .table-hover tbody tr:hover {
      background-color: rgba(67, 97, 238, 0.05);
    }

    .file-link {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
      display: inline-flex;
      align-items: center;
      transition: all 0.3s;
    }

    .file-link:hover {
      color: var(--secondary-color);
      transform: translateX(3px);
    }

    .file-link i {
      margin-right: 0.5rem;
    }

    .badge {
      padding: 0.5rem 0.75rem;
      border-radius: 8px;
      font-weight: 500;
      font-size: 0.75rem;
    }

    .empty-state {
      text-align: center;
      padding: 3rem;
    }

    .empty-state i {
      font-size: 4rem;
      color: #e2e8f0;
      margin-bottom: 1rem;
    }

    .empty-state p {
      color: var(--light-text);
      font-size: 1.1rem;
    }

    @media (max-width: 992px) {
      .content {
        margin-left: 0;
        padding: 20px;
      }
    }

    .file-type-icon {
      width: 40px;
      height: 40px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      margin-right: 10px;
      font-size: 1.2rem;
      color: white;
    }

    .file-type-pdf {
      background-color: #e74c3c;
    }

    .file-type-doc {
      background-color: #3498db;
    }

    .file-type-mp4 {
      background-color: #9b59b6;
    }

    .grade-badge {
      background-color: #f8f9fa;
      color: var(--dark-text);
      border: 1px solid #e2e8f0;
      font-weight: 500;
      padding: 0.5rem 0.75rem;
      border-radius: 8px;
    }
  </style>
</head>
<body>

<?php include_once __DIR__ . '/../../assets/teacher_sidebar.php'; ?>

<div class="content">
  <div class="page-header">
    <h2 class="mb-2"><i class="fas fa-book-open me-2"></i>Subject Details</h2>
    <p class="mb-0">Manage learning materials for <strong><?= htmlspecialchars($subject_name) ?></strong></p>

    <?php if (count($teacher_subjects) > 1): ?>
    <div class="mt-3">
      <form action="" method="GET" class="d-flex align-items-center">
        <label class="me-2 text-white">Switch Subject:</label>
        <select name="subject_id" class="form-select form-select-sm me-2" style="max-width: 200px; background-color: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3);" onchange="this.form.submit()">
          <?php foreach ($teacher_subjects as $subject): ?>
            <option value="<?= $subject['subject_id'] ?>" <?= $subject['subject_id'] == $subject_id ? 'selected' : '' ?>>
              <?= htmlspecialchars($subject['subject_name']) ?>
            </option>
          <?php endforeach; ?>
        </select>
      </form>
    </div>
    <?php endif; ?>
  </div>

  <div class="row">
    <div class="col-lg-5">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0"><i class="fas fa-upload me-2"></i>Upload New Material</h5>
        </div>
        <div class="card-body">
          <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
              <i class="fas fa-check-circle me-2"></i> <?= $success_message ?>
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
          <?php endif; ?>

          <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
              <i class="fas fa-exclamation-circle me-2"></i> <?= $error_message ?>
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
          <?php endif; ?>

          <form action="" method="POST" enctype="multipart/form-data">
            <?php if (count($teacher_subjects) > 1): ?>
            <div class="mb-3">
              <label for="subject_id" class="form-label">Select Subject</label>
              <select class="form-select" name="subject_id" id="subject_id" required>
                <?php foreach ($teacher_subjects as $subject): ?>
                  <option value="<?= $subject['subject_id'] ?>" <?= $subject['subject_id'] == $subject_id ? 'selected' : '' ?>>
                    <?= htmlspecialchars($subject['subject_name']) ?>
                  </option>
                <?php endforeach; ?>
              </select>
            </div>
            <?php else: ?>
              <input type="hidden" name="subject_id" value="<?= $subject_id ?>">
            <?php endif; ?>

            <div class="mb-3">
              <label for="grade" class="form-label">Select Grade</label>
              <select class="form-select" name="grade" id="grade" required>
                <option value="">Select Grade</option>
                <?php for ($i = 6; $i <= 11; $i++): ?>
                  <option value="Grade <?= $i ?>">Grade <?= $i ?></option>
                <?php endfor; ?>
                <option value="A/L">A/L</option>
              </select>
            </div>

            <div class="mb-3">
              <label class="form-label">Material Title</label>
              <input type="text" name="title" class="form-control" placeholder="Enter a descriptive title" required>
            </div>

            <div class="mb-4">
              <label class="form-label">Upload File</label>
              <div class="input-group">
                <input type="file" name="material_file" class="form-control" accept=".pdf,.doc,.docx,.mp4" required>
                <span class="input-group-text bg-light text-muted">
                  <small>PDF, DOCX, MP4</small>
                </span>
              </div>
              <small class="text-muted mt-2 d-block">Maximum file size: 10MB</small>
            </div>

            <button type="submit" class="btn btn-primary w-100">
              <i class="fas fa-cloud-upload-alt me-2"></i>Upload Material
            </button>
          </form>
        </div>
      </div>
    </div>

    <div class="col-lg-7">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0"><i class="fas fa-folder-open me-2"></i>Uploaded Materials</h5>
          <span class="badge bg-primary"><?= $materials->num_rows ?> Files</span>
        </div>
        <div class="card-body p-0">
          <?php if ($materials->num_rows > 0): ?>
            <div class="table-responsive">
              <table class="table table-hover mb-0">
                <thead>
                  <tr>
                    <th>Material</th>
                    <th>Grade</th>
                    <?php if (count($teacher_subjects) > 1): ?>
                    <th>Subject</th>
                    <?php endif; ?>
                    <th>Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <?php while ($row = $materials->fetch_assoc()):
                    // Determine file type icon
                    $file_extension = pathinfo($row['file_path'], PATHINFO_EXTENSION);
                    $icon_class = 'fas fa-file';
                    $type_class = 'file-type-pdf';

                    if (in_array(strtolower($file_extension), ['doc', 'docx'])) {
                      $icon_class = 'fas fa-file-word';
                      $type_class = 'file-type-doc';
                    } elseif (strtolower($file_extension) === 'pdf') {
                      $icon_class = 'fas fa-file-pdf';
                      $type_class = 'file-type-pdf';
                    } elseif (strtolower($file_extension) === 'mp4') {
                      $icon_class = 'fas fa-file-video';
                      $type_class = 'file-type-mp4';
                    }
                  ?>
                    <tr>
                      <td>
                        <div class="d-flex align-items-center">
                          <div class="file-type-icon <?= $type_class ?>">
                            <i class="<?= $icon_class ?>"></i>
                          </div>
                          <div>
                            <div class="fw-medium"><?= htmlspecialchars($row['title']) ?></div>
                            <small class="text-muted"><?= strtoupper($file_extension) ?> file</small>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span class="grade-badge">
                          <?= htmlspecialchars($row['grade']) ?>
                        </span>
                      </td>
                      <?php if (count($teacher_subjects) > 1): ?>
                      <td>
                        <span class="badge bg-primary">
                          <?= htmlspecialchars($row['subject_name']) ?>
                        </span>
                      </td>
                      <?php endif; ?>
                      <td>
                        <div><?= date("d M Y", strtotime($row['uploaded_at'])) ?></div>
                        <small class="text-muted"><?= date("h:i A", strtotime($row['uploaded_at'])) ?></small>
                      </td>
                      <td>
                        <div class="d-flex gap-2">
                          <a href="../../<?= htmlspecialchars($row['file_path']) ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye me-1"></i> View
                          </a>

                        </div>
                      </td>
                    </tr>
                  <?php endwhile; ?>
                </tbody>
              </table>
            </div>
          <?php else: ?>
            <div class="empty-state">
              <i class="fas fa-folder-open"></i>
              <p>No materials uploaded yet</p>
              <small class="text-muted">Upload your first teaching material using the form</small>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
