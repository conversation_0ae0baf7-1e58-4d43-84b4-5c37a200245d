<?php
session_start();
// Fix the include path based on how the script is called
$base_path = __DIR__ . '/../../';
include_once $base_path . 'backend/server/db_connect.php';

// Check if user is logged in and is a teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../../frontend/login.php");
    exit();
}

// Get teacher ID
$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT t.teacher_id, u.first_name, u.last_name, t.popup_notifications, t.email_notifications
                        FROM teachers t
                        JOIN users u ON t.user_id = u.id
                        WHERE t.user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();

if (!$teacher) {
    echo "Teacher not found.";
    exit();
}

$teacher_id = $teacher['teacher_id'];
$teacher_name = $teacher['first_name'] . ' ' . $teacher['last_name'];

// Check if tables exist
$tables_status = [];

// Check notifications table
$result = $conn->query("SHOW TABLES LIKE 'notifications'");
$tables_status['notifications'] = $result->num_rows > 0;

// Check email_logs table
$result = $conn->query("SHOW TABLES LIKE 'email_logs'");
$tables_status['email_logs'] = $result->num_rows > 0;

// Check teacher_timetable table
$result = $conn->query("SHOW TABLES LIKE 'teacher_timetable'");
$tables_status['teacher_timetable'] = $result->num_rows > 0;

// Get notification settings
$notification_settings = [
    'popup_notifications' => $teacher['popup_notifications'] == 1,
    'email_notifications' => $teacher['email_notifications'] == 1
];

// Get recent notifications
$recent_notifications = [];
if ($tables_status['notifications']) {
    $stmt = $conn->prepare("SELECT id, message, created_at, scheduled_time, is_read
                           FROM notifications
                           WHERE teacher_id = ?
                           ORDER BY scheduled_time DESC, created_at DESC
                           LIMIT 5");
    $stmt->bind_param("i", $teacher_id);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $recent_notifications[] = $row;
    }
}

// Get timetable entries
$timetable_entries = [];
if ($tables_status['teacher_timetable']) {
    $stmt = $conn->prepare("SELECT id, grade, day_of_week, start_time, end_time
                           FROM teacher_timetable
                           WHERE teacher_id = ?
                           ORDER BY FIELD(day_of_week, 'Monday','Tuesday','Wednesday','Thursday','Friday','Saturday','Sunday'), start_time");
    $stmt->bind_param("i", $teacher_id);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $timetable_entries[] = $row;
    }
}

// Process form submission
$message = "";
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_test_notification'])) {
        // Create a test notification
        $notification_text = "⚠️ IMPORTANT: Test notification for " . $teacher_name . " at " . date('h:i A');

        $stmt = $conn->prepare("INSERT INTO notifications (teacher_id, message, scheduled_time, is_read) VALUES (?, ?, NOW(), 0)");
        $stmt->bind_param("is", $teacher_id, $notification_text);

        if ($stmt->execute()) {
            $message = "<div class='alert alert-success'>Test notification created successfully!</div>";
        } else {
            $message = "<div class='alert alert-danger'>Error creating notification: " . $conn->error . "</div>";
        }
    } elseif (isset($_POST['run_check_reminders'])) {
        // Run the check_reminders.php script
        include 'check_reminders.php';
        $message = "<div class='alert alert-success'>Reminder check completed. Check the notifications list for any new reminders.</div>";
    } elseif (isset($_POST['update_settings'])) {
        // Update notification settings
        $popup_notifications = isset($_POST['popup_notifications']) ? 1 : 0;
        $email_notifications = isset($_POST['email_notifications']) ? 1 : 0;

        $stmt = $conn->prepare("UPDATE teachers SET popup_notifications = ?, email_notifications = ? WHERE teacher_id = ?");
        $stmt->bind_param("iii", $popup_notifications, $email_notifications, $teacher_id);

        if ($stmt->execute()) {
            $message = "<div class='alert alert-success'>Notification settings updated successfully!</div>";
            // Update local settings
            $notification_settings['popup_notifications'] = $popup_notifications == 1;
            $notification_settings['email_notifications'] = $email_notifications == 1;
        } else {
            $message = "<div class='alert alert-danger'>Error updating settings: " . $conn->error . "</div>";
        }
    }
}

// HTML output
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Notification System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f4f6f9;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(to right, #4361ee, #4895ef);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            font-weight: 600;
        }
        .btn-primary {
            background-color: #4361ee;
            border-color: #4361ee;
        }
        .btn-primary:hover {
            background-color: #3f37c9;
            border-color: #3f37c9;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-ok {
            background-color: #2ecc71;
        }
        .status-error {
            background-color: #e74c3c;
        }
        .notification-item {
            border-left: 4px solid #4361ee;
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .notification-item.unread {
            border-left-color: #ffc107;
            background-color: #fff8e1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Notification System Test</h1>

        <?php echo $message; ?>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-cogs me-2"></i>System Status
                    </div>
                    <div class="card-body">
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Notifications Table
                                <span class="status-indicator <?php echo $tables_status['notifications'] ? 'status-ok' : 'status-error'; ?>"></span>
                                <?php echo $tables_status['notifications'] ? 'OK' : 'Missing'; ?>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Email Logs Table
                                <span class="status-indicator <?php echo $tables_status['email_logs'] ? 'status-ok' : 'status-error'; ?>"></span>
                                <?php echo $tables_status['email_logs'] ? 'OK' : 'Missing'; ?>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Teacher Timetable
                                <span class="status-indicator <?php echo $tables_status['teacher_timetable'] ? 'status-ok' : 'status-error'; ?>"></span>
                                <?php echo $tables_status['teacher_timetable'] ? 'OK' : 'Missing'; ?>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Timetable Entries
                                <span class="status-indicator <?php echo count($timetable_entries) > 0 ? 'status-ok' : 'status-error'; ?>"></span>
                                <?php echo count($timetable_entries) > 0 ? count($timetable_entries) . ' entries' : 'No entries'; ?>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-bell me-2"></i>Notification Settings
                    </div>
                    <div class="card-body">
                        <form method="post">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="popup_notifications" name="popup_notifications" <?php echo $notification_settings['popup_notifications'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="popup_notifications">Enable Popup Notifications</label>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" <?php echo $notification_settings['email_notifications'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="email_notifications">Enable Email Notifications</label>
                            </div>
                            <button type="submit" name="update_settings" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Settings
                            </button>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-tools me-2"></i>Test Tools
                    </div>
                    <div class="card-body">
                        <form method="post" class="mb-3">
                            <button type="submit" name="create_test_notification" class="btn btn-warning mb-2 w-100">
                                <i class="fas fa-bell me-2"></i>Create Test Notification
                            </button>
                        </form>

                        <form method="post">
                            <button type="submit" name="run_check_reminders" class="btn btn-primary w-100">
                                <i class="fas fa-sync-alt me-2"></i>Run Reminder Check Now
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-bell me-2"></i>Recent Notifications
                    </div>
                    <div class="card-body">
                        <?php if (count($recent_notifications) > 0): ?>
                            <?php foreach ($recent_notifications as $notification): ?>
                                <div class="notification-item <?php echo $notification['is_read'] ? '' : 'unread'; ?>">
                                    <div class="d-flex justify-content-between">
                                        <strong><?php echo htmlspecialchars($notification['message']); ?></strong>
                                        <?php if (!$notification['is_read']): ?>
                                            <span class="badge bg-warning">Unread</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-muted small mt-1">
                                        <?php
                                        $time = $notification['scheduled_time'] ?: $notification['created_at'];
                                        echo date('M j, Y g:i A', strtotime($time));
                                        ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-center py-3">No notifications found.</p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-calendar-alt me-2"></i>Your Timetable
                    </div>
                    <div class="card-body">
                        <?php if (count($timetable_entries) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Day</th>
                                            <th>Grade</th>
                                            <th>Time</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($timetable_entries as $entry): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($entry['day_of_week']); ?></td>
                                                <td><?php echo htmlspecialchars($entry['grade']); ?></td>
                                                <td>
                                                    <?php
                                                    echo date('h:i A', strtotime($entry['start_time'])) . ' - ' .
                                                         date('h:i A', strtotime($entry['end_time']));
                                                    ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-center py-3">No timetable entries found. Please add classes to your timetable.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <a href="../../frontend/timetables/teacher_timetable.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Timetable
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
