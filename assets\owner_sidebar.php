<?php
$currentPage = basename($_SERVER['PHP_SELF']); // e.g. owner_dashboard.php

// Determine the base path for links based on the current script path
$scriptPath = $_SERVER['SCRIPT_NAME'];
$baseDir = '';

// Check if we're in the frontend directory or subdirectories
if (strpos($scriptPath, '/frontend/') !== false) {
    // We're in the frontend directory or a subdirectory
    $pathParts = explode('/frontend/', $scriptPath);
    $subPath = $pathParts[1] ?? '';

    // Count directory levels in the subpath
    $dirCount = substr_count($subPath, '/');

    if ($dirCount > 0) {
        // We're in a subdirectory of frontend
        $baseDir = str_repeat('../', $dirCount);
    }
}
?>

<!-- Mobile menu toggle button -->
<button class="mobile-menu-toggle d-md-none">
  <i class="fas fa-bars"></i>
</button>

<div class="sidebar">
  <h3><i class="fa-solid fa-bars"></i> <span>Gurukula</span></h3>
  <a href="<?= $baseDir ?>dashboards/owner_dashboard.php" class="<?= $currentPage === 'owner_dashboard.php' ? 'active' : '' ?>">
    <i class="fas fa-home"></i> <span>Dashboard</span>
  </a>
  <a href="<?= $baseDir ?>financial_report.php" class="<?= $currentPage === 'financial_report.php' ? 'active' : '' ?>">
    <i class="fas fa-money-bill-wave"></i> <span>Financial Updates</span>
  </a>
  <a href="<?= $baseDir ?>timetables/owner_timetable.php" class="<?= $currentPage === 'owner_timetable.php' ? 'active' : '' ?>">
    <i class="fa-solid fa-calendar-minus"></i> <span>View Timetable</span>
  </a>
  <?php
    // Calculate the correct path to logout.php based on current location
    $logoutPath = '';
    if (strpos($scriptPath, '/frontend/dashboards/') !== false ||
        strpos($scriptPath, '/frontend/timetables/') !== false) {
        $logoutPath = '../../backend/server/logout.php';
    } else if (strpos($scriptPath, '/frontend/') !== false) {
        $logoutPath = '../backend/server/logout.php';
    } else {
        $logoutPath = 'backend/server/logout.php';
    }
  ?>
  <a href="<?= $logoutPath ?>" class="text-danger">
    <i class="fas fa-sign-out-alt"></i> <span>Logout</span>
  </a>
</div>

<!-- Include responsive CSS and JS -->
<?php
$assetsPath = $baseDir ? $baseDir . '../../assets/' : '../assets/';
?>
<link rel="stylesheet" href="<?= $assetsPath ?>css/responsive.css">
<script src="<?= $assetsPath ?>js/responsive-nav.js" defer></script>

<style>
  /* Sidebar styles */
  .sidebar {
    height: 100vh;
    width: 260px;
    position: fixed;
    top: 0;
    left: 0;
    background-color: #02005F;
    padding: 25px;
    color: #fff;
    transition: all 0.3s;
    z-index: 1040;
  }
  .sidebar h3 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 1.8rem;
  }
  .sidebar a {
    color: #fff;
    text-decoration: none;
    display: block;
    padding: 12px;
    margin-bottom: 12px;
    border-radius: 8px;
    font-size: 1.2rem;
    transition: 0.3s;
  }
  .sidebar a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
  }
  .sidebar a:hover,
  .sidebar a.active {
    background-color: #67BAFD;
    transform: scale(1.05);
  }
</style>
