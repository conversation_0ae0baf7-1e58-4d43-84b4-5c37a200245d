<?php
include_once '../../backend/server/db_connect.php';

// Set response type to JSON
header('Content-Type: application/json');

try {
    // Check if the database connection is valid
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    $sql = "SELECT t.grade, t.day_of_week, t.start_time, t.end_time, 
                   s.subject_name, u.first_name, u.last_name 
            FROM teacher_timetable t 
            JOIN subjects s ON t.subject_id = s.subject_id 
            JOIN teachers te ON t.teacher_id = te.teacher_id
            JOIN users u ON te.user_id = u.id
            ORDER BY FIELD(t.day_of_week, 'Monday','Tuesday','Wednesday','Thursday','Friday','Saturday','Sunday'), t.start_time";

    $result = $conn->query($sql);

    if (!$result) {
        throw new Exception("Query failed: " . $conn->error);
    }

    $timetables = [];
    while ($row = $result->fetch_assoc()) {
        $timetables[] = [
            'teacher_name' => $row['first_name'] . ' ' . $row['last_name'],
            'subject' => $row['subject_name'],
            'grade' => $row['grade'],
            'day' => $row['day_of_week'],
            'time' => date("h:i A", strtotime($row['start_time'])) . " - " . date("h:i A", strtotime($row['end_time']))
        ];
    }
    
    echo json_encode($timetables);
    
} catch (Exception $e) {
    // Log the error
    error_log("Error in get_all_timetables.php: " . $e->getMessage());
    
    // Return error response
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch timetables: ' . $e->getMessage()]);
}
?>
