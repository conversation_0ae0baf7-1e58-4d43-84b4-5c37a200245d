<?php
session_start();
include '../server/db_connect.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../frontend/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Redirect to appropriate settings page based on role
$settings_page = "../../frontend/settings/{$user_role}_settings.php";
if ($user_role === 'student') {
    $settings_page = "../../frontend/settings/student_settings.php?tab=password";
} elseif ($user_role === 'teacher') {
    $settings_page = "../../frontend/settings/teacher_settings.php";
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate password requirements
    $password_valid = true;
    $error_message = "";

    // Check if passwords match
    if ($new_password !== $confirm_password) {
        $password_valid = false;
        $error_message = "New passwords do not match.";
    }

    // Check password length
    if (strlen($new_password) < 8) {
        $password_valid = false;
        $error_message = "Password must be at least 8 characters long.";
    }

    // Check for uppercase letters
    if (!preg_match('/[A-Z]/', $new_password)) {
        $password_valid = false;
        $error_message = "Password must contain at least one uppercase letter.";
    }

    // Check for lowercase letters
    if (!preg_match('/[a-z]/', $new_password)) {
        $password_valid = false;
        $error_message = "Password must contain at least one lowercase letter.";
    }

    // Check for numbers
    if (!preg_match('/[0-9]/', $new_password)) {
        $password_valid = false;
        $error_message = "Password must contain at least one number.";
    }

    // Check for symbols
    if (!preg_match('/[^A-Za-z0-9]/', $new_password)) {
        $password_valid = false;
        $error_message = "Password must contain at least one symbol.";
    }

    if (!$password_valid) {
        $_SESSION['error'] = $error_message;
        header("Location: $settings_page");
        exit();
    }

    // Get current password hash from DB
    $stmt = $conn->prepare("SELECT password FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $hashed_password = $user['password'] ?? '';
    $stmt->close();

    if (password_verify($current_password, $hashed_password)) {
        $new_hashed = password_hash($new_password, PASSWORD_DEFAULT);
        $update = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
        $update->bind_param("si", $new_hashed, $user_id);

        if ($update->execute()) {
            $_SESSION['success'] = "Password updated successfully.";
        } else {
            $_SESSION['error'] = "Error updating password. Please try again.";
        }

        $update->close();
    } else {
        $_SESSION['error'] = "Current password is incorrect.";
    }

    header("Location: $settings_page");
    exit();
}

// If not POST request, redirect to settings page
header("Location: $settings_page");
exit();
?>
