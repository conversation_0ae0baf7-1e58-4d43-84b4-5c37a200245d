<?php
echo "<h1>GD Library Fix Tool</h1>";

// Get the loaded php.ini file
$php_ini = php_ini_loaded_file();
echo "<p>Loaded php.ini: <strong>$php_ini</strong></p>";

if (!file_exists($php_ini)) {
    echo "<p style='color:red;'>Error: Cannot find php.ini file!</p>";
    exit;
}

// Read the php.ini file
$ini_content = file_get_contents($php_ini);
$lines = explode("\n", $ini_content);

// Find the line with extension=gd
$gd_line_number = null;
$gd_line = null;
$is_commented = false;

foreach ($lines as $i => $line) {
    $line = trim($line);
    if ($line === 'extension=gd') {
        $gd_line_number = $i + 1;
        $gd_line = $line;
        $is_commented = false;
        break;
    } elseif ($line === ';extension=gd') {
        $gd_line_number = $i + 1;
        $gd_line = $line;
        $is_commented = true;
        break;
    }
}

// Display the results
if ($gd_line_number !== null) {
    echo "<p>Found GD configuration at line $gd_line_number: <code>$gd_line</code></p>";
    
    if ($is_commented) {
        echo "<p style='color:red;'>GD extension is commented out (disabled).</p>";
        
        // Show the surrounding lines for context
        echo "<h2>Context in php.ini:</h2>";
        echo "<pre style='background-color:#f5f5f5; padding:10px; border:1px solid #ddd;'>";
        $start = max(0, $gd_line_number - 10);
        $end = min(count($lines), $gd_line_number + 10);
        
        for ($i = $start; $i < $end; $i++) {
            $line_num = $i + 1;
            $line_content = htmlspecialchars($lines[$i]);
            
            if ($line_num == $gd_line_number) {
                echo "<span style='background-color:yellow; font-weight:bold;'>Line $line_num: $line_content</span>\n";
            } else {
                echo "Line $line_num: $line_content\n";
            }
        }
        echo "</pre>";
        
        echo "<h2>Fix Instructions:</h2>";
        echo "<ol>";
        echo "<li>Stop Apache in XAMPP Control Panel</li>";
        echo "<li>Open <code>$php_ini</code> in a text editor (run as administrator)</li>";
        echo "<li>Go to line $gd_line_number</li>";
        echo "<li>Change <code>$gd_line</code> to <code>extension=gd</code> (remove the semicolon)</li>";
        echo "<li>Save the file</li>";
        echo "<li>Start Apache in XAMPP Control Panel</li>";
        echo "</ol>";
        
        echo "<h3>Alternative: Use the Windows Extension Manager</h3>";
        echo "<ol>";
        echo "<li>Stop Apache in XAMPP Control Panel</li>";
        echo "<li>Open XAMPP Control Panel</li>";
        echo "<li>Click on 'Config' button next to Apache</li>";
        echo "<li>Select 'PHP (php.ini)'</li>";
        echo "<li>Find the line <code>;extension=gd</code></li>";
        echo "<li>Remove the semicolon (;) so it becomes <code>extension=gd</code></li>";
        echo "<li>Save the file</li>";
        echo "<li>Start Apache in XAMPP Control Panel</li>";
        echo "</ol>";
    } else {
        echo "<p style='color:green;'>GD extension is already enabled in php.ini!</p>";
        echo "<p>If you're still having issues, try:</p>";
        echo "<ol>";
        echo "<li>Make sure you've restarted Apache after making changes</li>";
        echo "<li>Check if PHP has write permissions to create images</li>";
        echo "<li>Verify that the GD library is properly installed with XAMPP</li>";
        echo "</ol>";
    }
} else {
    echo "<p style='color:red;'>Could not find GD extension configuration in php.ini!</p>";
    
    // Look for extension_dir setting
    $extension_dir = null;
    foreach ($lines as $line) {
        if (preg_match('/^extension_dir\s*=\s*"([^"]+)"/', $line, $matches)) {
            $extension_dir = $matches[1];
            break;
        }
    }
    
    if ($extension_dir) {
        echo "<p>Extension directory: <code>$extension_dir</code></p>";
        
        // Check if php_gd2.dll exists
        $gd_dll = $extension_dir . DIRECTORY_SEPARATOR . "php_gd2.dll";
        if (file_exists($gd_dll)) {
            echo "<p style='color:green;'>Found GD library file: <code>$gd_dll</code></p>";
        } else {
            echo "<p style='color:red;'>GD library file not found: <code>$gd_dll</code></p>";
            echo "<p>The GD library may not be installed with your XAMPP.</p>";
        }
    }
    
    echo "<h2>Possible Solutions:</h2>";
    echo "<ol>";
    echo "<li>Add <code>extension=gd</code> to your php.ini file in the extensions section</li>";
    echo "<li>Reinstall XAMPP to ensure all required libraries are included</li>";
    echo "<li>Update your XAMPP to the latest version</li>";
    echo "</ol>";
}
?>
