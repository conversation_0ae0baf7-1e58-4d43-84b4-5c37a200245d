<?php
session_start();
include_once '../../backend/server/db_connect.php'; // Ensure this path is correct

// Check if the user is logged in and is a teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get teacher_id and subjects
$stmt = $conn->prepare("
    SELECT t.teacher_id, ts.subject_id, s.subject_name
    FROM teachers t
    JOIN teacher_subjects ts ON t.teacher_id = ts.teacher_id
    JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE t.user_id = ?
");

$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_subjects = [];
$teacher_id = null;

while ($row = $result->fetch_assoc()) {
    if (!$teacher_id) {
        $teacher_id = $row['teacher_id'];
    }
    $teacher_subjects[] = [
        'subject_id' => $row['subject_id'],
        'subject_name' => $row['subject_name']
    ];
}

if (empty($teacher_subjects)) {
    $error_message = "No subjects or teacher found for your account.";
} else {
    // If a specific subject is selected (e.g., from URL parameter)
    $subject_id = isset($_GET['subject_id']) ? $_GET['subject_id'] : $teacher_subjects[0]['subject_id'];
    $subject_name = '';

    foreach ($teacher_subjects as $subject) {
        if ($subject['subject_id'] == $subject_id) {
            $subject_name = $subject['subject_name'];
            break;
        }
    }
}
$stmt->close();


// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $grade = $_POST['grade'] ?? '';
    $day_of_week = $_POST['day_of_week'] ?? '';
    $start_time = $_POST['start_time'] ?? '';
    $end_time = $_POST['end_time'] ?? '';
    $subject_id = $_POST['subject_id'] ?? 0;

    if (empty($subject_id) || empty($grade) || empty($day_of_week) || empty($start_time) || empty($end_time)) {
        $error_message = "All fields are required.";
    }
    // Add time validation
    elseif (strtotime($end_time) <= strtotime($start_time)) {
        $error_message = "End time must be later than start time.";
    }
    else {
        $stmt = $conn->prepare("INSERT INTO teacher_timetable (teacher_id, subject_id, grade, day_of_week, start_time, end_time) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("iissss", $teacher_id, $subject_id, $grade, $day_of_week, $start_time, $end_time);

        if ($stmt->execute()) {
            header("Location: teacher_timetable.php?subject_id=$subject_id&status=success");
            exit();
        } else {
            $error_message = "Error: " . $stmt->error;
        }
        $stmt->close();
    }
}

// Add this at the top of your PHP section to handle delete requests
if (isset($_GET['delete_id']) && is_numeric($_GET['delete_id'])) {
    $delete_id = $_GET['delete_id'];
    $delete_stmt = $conn->prepare("DELETE FROM teacher_timetable WHERE id = ? AND teacher_id = ?");
    $delete_stmt->bind_param("ii", $delete_id, $teacher_id);

    if ($delete_stmt->execute()) {
        header("Location: teacher_timetable.php?status=deleted");
        exit();
    } else {
        $error_message = "Error deleting timetable entry: " . $delete_stmt->error;
    }
    $delete_stmt->close();
}

// Fetch weekly timetable for the selected subject
$timetable_stmt = $conn->prepare("
    SELECT tt.id, tt.grade, tt.day_of_week, tt.start_time, tt.end_time, s.subject_name
    FROM teacher_timetable tt
    JOIN subjects s ON tt.subject_id = s.subject_id
    WHERE tt.teacher_id = ? AND tt.subject_id = ?
");
$timetable_stmt->bind_param("ii", $teacher_id, $subject_id);
$timetable_stmt->execute();
$timetable_result = $timetable_stmt->get_result();

$weekly_timetable = [
    "Monday" => [],
    "Tuesday" => [],
    "Wednesday" => [],
    "Thursday" => [],
    "Friday" => [],
    "Saturday" => [],
    "Sunday" => []
];

while ($row = $timetable_result->fetch_assoc()) {
    $weekly_timetable[$row['day_of_week']][] = $row;
}

$timetable_stmt->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Teacher Timetable</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-bg: #f4f6f9;
      --dark-text: #2d3748;
      --light-text: #718096;
      --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: var(--light-bg);
      color: var(--dark-text);
      margin: 0;
      padding: 0;
    }

    .content {
      margin-left: 260px;
      padding: 30px;
    }

    .page-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
    }

    .page-header::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 200%;
      background: rgba(255, 255, 255, 0.1);
      transform: rotate(30deg);
    }

    .main-content {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .timetable-form-card {
      background: white;
      border-radius: 16px;
      box-shadow: var(--card-shadow);
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .timetable-form-card:hover {
      box-shadow: var(--hover-shadow);
    }

    .card-header {
      background: linear-gradient(to right, var(--primary-color), var(--accent-color));
      color: white;
      padding: 1.25rem 1.5rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }

    .card-body {
      padding: 1.5rem;
    }

    .form-label {
      font-weight: 500;
      color: var(--dark-text);
      margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
      border-radius: 10px;
      padding: 0.75rem 1rem;
      border: 1px solid #e2e8f0;
      background-color: #f8f9fa;
      transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
    }

    .btn {
      border-radius: 10px;
      padding: 0.75rem 1.5rem;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .btn-primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }

    .btn-primary:hover {
      background-color: var(--secondary-color);
      border-color: var(--secondary-color);
      transform: translateY(-2px);
    }

    .btn-danger {
      background-color: var(--danger-color);
      border-color: var(--danger-color);
    }

    .btn-danger:hover {
      background-color: #c0392b;
      border-color: #c0392b;
      transform: translateY(-2px);
    }

    .btn-secondary {
      background-color: #6c757d;
      border-color: #6c757d;
    }

    .btn-secondary:hover {
      background-color: #5a6268;
      border-color: #5a6268;
      transform: translateY(-2px);
    }

    .btn-sm {
      padding: 0.4rem 0.75rem;
      font-size: 0.875rem;
    }

    .timetable-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 1.5rem;
      margin-top: 2rem;
    }

    .day-card {
      background: white;
      border-radius: 16px;
      box-shadow: var(--card-shadow);
      overflow: hidden;
      height: 100%;
      transition: all 0.3s ease;
    }

    .day-card:hover {
      box-shadow: var(--hover-shadow);
      transform: translateY(-5px);
    }

    .day-header {
      background: linear-gradient(to right, var(--primary-color), var(--accent-color));
      color: white;
      padding: 1rem;
      text-align: center;
      font-weight: 600;
      letter-spacing: 0.5px;
    }

    .day-body {
      padding: 1.25rem;
      min-height: 200px;
    }

    .time-slot {
      background-color: #f8f9fa;
      border-radius: 10px;
      padding: 1rem;
      margin-bottom: 1rem;
      border-left: 4px solid var(--accent-color);
      transition: all 0.2s ease;
    }

    .time-slot:hover {
      background-color: #edf2f7;
      transform: translateX(5px);
    }

    .time-slot:last-child {
      margin-bottom: 0;
    }

    .time-slot-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;
    }

    .time-slot-grade {
      font-weight: 600;
      color: var(--dark-text);
    }

    .time-slot-time {
      font-size: 0.875rem;
      color: var(--light-text);
      background-color: rgba(67, 97, 238, 0.1);
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
    }

    .time-slot-actions {
      display: flex;
      gap: 0.5rem;
      margin-top: 0.75rem;
    }

    .empty-day {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      min-height: 150px;
      color: var(--light-text);
    }

    .empty-day i {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }

    .alert {
      border-radius: 10px;
      padding: 1rem 1.25rem;
      margin-bottom: 1.5rem;
    }

    /* Notification styles */
    .position-fixed {
      z-index: 1030;
    }

    #notificationBtn {
      background: white;
      border: none;
      width: 42px;
      height: 42px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }

    #notificationBtn:hover {
      background-color: #f8f9fa;
      transform: translateY(-2px);
    }

    #notificationBox {
      width: 350px;
      border-radius: 12px;
      box-shadow: 0 10px 25px rgba(0,0,0,0.15);
      max-height: 450px;
      overflow-y: auto;
    }

    #notificationBox .card-header {
      padding: 1rem 1.25rem;
    }

    #notificationList {
      max-height: 350px;
      overflow-y: auto;
    }

    #notificationList .list-group-item {
      padding: 1rem 1.25rem;
      border-left: none;
      border-right: none;
      transition: all 0.2s ease;
    }

    #notificationList .list-group-item:hover {
      background-color: #f8f9fa;
    }

    .notification-item {
      border-left: 4px solid transparent !important;
    }

    .notification-item.bg-light-warning {
      border-left-color: #ffc107 !important;
      background-color: #fff8e1 !important;
    }

    .notification-item.bg-light-info {
      border-left-color: #17a2b8 !important;
      background-color: #e3f2fd !important;
    }

    .notification-title {
      font-weight: 500;
      margin-bottom: 5px;
    }

    .notification-details {
      color: #6c757d;
    }

    .notification-time {
      font-size: 0.8rem;
      color: #6c757d;
    }

    /* Modal styles */
    .modal-content {
      border-radius: 16px;
      border: none;
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    .modal-header {
      background: linear-gradient(to right, var(--primary-color), var(--accent-color));
      color: white;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
      padding: 1.25rem 1.5rem;
    }

    .modal-title {
      font-weight: 600;
    }

    .modal-body {
      padding: 1.5rem;
    }

    .modal-footer {
      padding: 1rem 1.5rem;
      border-top: 1px solid #edf2f7;
    }

    @media (max-width: 768px) {
      .content {
        margin-left: 0;
        padding: 15px;
      }

      .timetable-grid {
        grid-template-columns: 1fr;
      }

      .page-header {
        padding: 1.5rem;
      }
    }
  </style>
</head>
<body>
<?php include_once __DIR__ . '/../../assets/teacher_sidebar.php'; ?>

<div class="content">
  <div class="page-header">
    <h2 class="mb-2"><i class="fas fa-calendar-alt me-2"></i>My Teaching Schedule</h2>
    <p class="mb-0">Manage your weekly teaching timetable for <strong><?= htmlspecialchars($subject_name) ?></strong></p>

    <?php if (count($teacher_subjects) > 1): ?>
    <div class="mt-3">
      <form action="" method="GET" class="d-flex align-items-center">
        <label class="me-2 text-white">Switch Subject:</label>
        <select name="subject_id" class="form-select form-select-sm me-2" style="max-width: 200px; background-color: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3);" onchange="this.form.submit()">
          <?php foreach ($teacher_subjects as $subject): ?>
            <option value="<?= $subject['subject_id'] ?>" <?= $subject['subject_id'] == $subject_id ? 'selected' : '' ?>>
              <?= htmlspecialchars($subject['subject_name']) ?>
            </option>
          <?php endforeach; ?>
        </select>
      </form>
    </div>
    <?php endif; ?>
  </div>

  <div class="main-content">
    <!-- Notification Icon -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 10000;">
      <button class="btn position-relative" id="notificationBtn">
        <i class="fas fa-bell fa-lg"></i>
        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notificationCount">
          0
        </span>
      </button>

      <!-- Notification dropdown box -->
      <div id="notificationBox" class="card shadow" style="display: none;">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
          <div>
            <i class="fas fa-bell me-2"></i><strong>Notifications</strong>
          </div>
          <div>
            <button id="refreshNotifications" class="btn btn-sm btn-light" title="Refresh Notifications">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
        </div>
        <ul class="list-group list-group-flush" id="notificationList">
          <li class="list-group-item text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <!-- Add New Time Slot Form -->
    <div class="timetable-form-card">
      <div class="card-header">
        <i class="fas fa-plus-circle"></i>
        <span>Add New Class Time</span>
      </div>
      <div class="card-body">
        <?php if (!empty($error_message)): ?>
          <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($error_message) ?>
          </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
          <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($_SESSION['error_message']) ?>
          </div>
          <?php unset($_SESSION['error_message']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['success_message'])): ?>
          <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($_SESSION['success_message']) ?>
          </div>
          <?php unset($_SESSION['success_message']); ?>
        <?php endif; ?>

        <?php if (isset($_GET['status']) && $_GET['status'] == 'success'): ?>
          <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>Time slot added successfully!
          </div>
        <?php endif; ?>

        <form action="teacher_timetable.php?subject_id=<?= $subject_id ?>" method="POST">
          <div class="row">
            <div class="col-md-6 mb-3">
              <label class="form-label">Subject</label>
              <?php if (count($teacher_subjects) > 1): ?>
              <select name="subject_id" class="form-select" required>
                <?php foreach ($teacher_subjects as $subject): ?>
                  <option value="<?= $subject['subject_id'] ?>" <?= $subject['subject_id'] == $subject_id ? 'selected' : '' ?>>
                    <?= htmlspecialchars($subject['subject_name']) ?>
                  </option>
                <?php endforeach; ?>
              </select>
              <?php else: ?>
              <input type="text" name="subject_name" value="<?= htmlspecialchars($subject_name) ?>" class="form-control" readonly>
              <input type="hidden" name="subject_id" value="<?= $subject_id ?>">
              <?php endif; ?>
            </div>
            <div class="col-md-6 mb-3">
              <label class="form-label">Grade</label>
              <input type="text" name="grade" class="form-control" placeholder="Enter grade (e.g. Grade 10)" required>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4 mb-3">
              <label class="form-label">Day</label>
              <select name="day_of_week" class="form-select" required>
                <option value="">Select Day</option>
                <option value="Monday">Monday</option>
                <option value="Tuesday">Tuesday</option>
                <option value="Wednesday">Wednesday</option>
                <option value="Thursday">Thursday</option>
                <option value="Friday">Friday</option>
                <option value="Saturday">Saturday</option>
                <option value="Sunday">Sunday</option>
              </select>
            </div>
            <div class="col-md-4 mb-3">
              <label class="form-label">Start Time</label>
              <input type="time" name="start_time" class="form-control" required>
            </div>
            <div class="col-md-4 mb-3">
              <label class="form-label">End Time</label>
              <input type="time" name="end_time" class="form-control" required>
            </div>
          </div>

          <div class="d-grid">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-plus-circle me-2"></i>Add to Schedule
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Notification Tools -->
    <div class="timetable-form-card mt-4">
      <div class="card-header">
        <i class="fas fa-bell"></i>
        <span>Class Reminder Tools</span>
      </div>
      <div class="card-body">
        <p>You can manage your notification preferences in the <a href="../../frontend/settings/teacher_settings.php" class="text-decoration-underline">Settings</a> page.</p>

        <div class="d-flex gap-3 flex-wrap">
          <button id="checkRemindersBtn" class="btn btn-warning">
            <i class="fas fa-sync-alt me-2"></i>Check for Reminders Now
          </button>
        </div>

        <!-- Reminder check result message -->
        <div id="reminderCheckResult" class="mt-3" style="display: none;"></div>
      </div>
    </div>

    <!-- Weekly Timetable Display -->
    <h4 class="mt-4 mb-3"><i class="fas fa-calendar-week me-2"></i>Weekly Schedule</h4>
    <div class="timetable-grid">
      <?php foreach ($weekly_timetable as $day => $slots): ?>
        <div class="day-card">
          <div class="day-header">
            <?= $day ?>
          </div>
          <div class="day-body">
            <?php if (count($slots) > 0): ?>
              <?php foreach ($slots as $slot): ?>
                <div class="time-slot">
                  <div class="time-slot-header">
                    <div class="time-slot-grade"><?= htmlspecialchars($slot['grade']) ?></div>
                    <div class="time-slot-time">
                      <i class="far fa-clock me-1"></i>
                      <?= date("h:i A", strtotime($slot['start_time'])) ?> - <?= date("h:i A", strtotime($slot['end_time'])) ?>
                    </div>
                  </div>
                  <div class="time-slot-actions">
                    <button type="button" class="btn btn-sm btn-primary edit-slot"
                            data-id="<?= $slot['id'] ?>"
                            data-grade="<?= htmlspecialchars($slot['grade']) ?>"
                            data-day="<?= $slot['day_of_week'] ?>"
                            data-start="<?= $slot['start_time'] ?>"
                            data-end="<?= $slot['end_time'] ?>"
                            data-subject-id="<?= $subject_id ?>">
                      <i class="fas fa-edit me-1"></i>Edit
                    </button>
                    <a href="teacher_timetable.php?delete_id=<?= $slot['id'] ?>"
                       class="btn btn-sm btn-danger"
                       onclick="return confirm('Are you sure you want to delete this time slot?')">
                      <i class="fas fa-trash me-1"></i>Delete
                    </a>
                  </div>
                </div>
              <?php endforeach; ?>
            <?php else: ?>
              <div class="empty-day">
                <i class="far fa-calendar-times"></i>
                <p>No classes scheduled</p>
              </div>
            <?php endif; ?>
          </div>
        </div>
      <?php endforeach; ?>
    </div>
  </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editModalLabel"><i class="fas fa-edit me-2"></i>Edit Time Slot</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="editForm" action="../../backend/timetables/update_timetable.php" method="POST">
        <div class="modal-body">
          <input type="hidden" name="slot_id" id="edit_id">

          <?php if (count($teacher_subjects) > 1): ?>
          <div class="mb-3">
            <label class="form-label">Subject</label>
            <select name="subject_id" id="edit_subject" class="form-select" required>
              <?php foreach ($teacher_subjects as $subject): ?>
                <option value="<?= $subject['subject_id'] ?>">
                  <?= htmlspecialchars($subject['subject_name']) ?>
                </option>
              <?php endforeach; ?>
            </select>
          </div>
          <?php else: ?>
          <input type="hidden" name="subject_id" value="<?= $subject_id ?>">
          <?php endif; ?>
          <div class="mb-3">
            <label class="form-label">Grade</label>
            <input type="text" name="grade" id="edit_grade" class="form-control" required>
          </div>
          <div class="mb-3">
            <label class="form-label">Day</label>
            <select name="day_of_week" id="edit_day" class="form-select" required>
              <option value="Monday">Monday</option>
              <option value="Tuesday">Tuesday</option>
              <option value="Wednesday">Wednesday</option>
              <option value="Thursday">Thursday</option>
              <option value="Friday">Friday</option>
              <option value="Saturday">Saturday</option>
              <option value="Sunday">Sunday</option>
            </select>
          </div>
          <div class="mb-3">
            <label class="form-label">Start Time</label>
            <input type="time" name="start_time" id="edit_start_time" class="form-control" required>
          </div>
          <div class="mb-3">
            <label class="form-label">End Time</label>
            <input type="time" name="end_time" id="edit_end_time" class="form-control" required>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Save Changes</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="../../assets/js/notifications.js"></script>

<script>
// Initialize everything when the document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Add form validation for the main form
    const form = document.querySelector('form');

    form.addEventListener('submit', function(event) {
        const startTime = document.querySelector('input[name="start_time"]').value;
        const endTime = document.querySelector('input[name="end_time"]').value;

        if (startTime && endTime) {
            if (startTime >= endTime) {
                event.preventDefault();
                alert('End time must be later than start time.');
            }
        }
    });

    // Add real-time validation for better user experience
    const startTimeInput = document.querySelector('input[name="start_time"]');
    const endTimeInput = document.querySelector('input[name="end_time"]');

    function validateTimes() {
        if (startTimeInput.value && endTimeInput.value) {
            if (startTimeInput.value >= endTimeInput.value) {
                endTimeInput.setCustomValidity('End time must be later than start time');
            } else {
                endTimeInput.setCustomValidity('');
            }
        }
    }

    startTimeInput.addEventListener('change', validateTimes);
    endTimeInput.addEventListener('change', validateTimes);

    // Add edit functionality
    // Edit slot button click
    const editButtons = document.querySelectorAll('.edit-slot');
    const editModal = new bootstrap.Modal(document.getElementById('editModal'));

    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const grade = this.getAttribute('data-grade');
            const day = this.getAttribute('data-day');
            const startTime = this.getAttribute('data-start');
            const endTime = this.getAttribute('data-end');
            const subjectId = this.getAttribute('data-subject-id') || <?= $subject_id ?>;

            document.getElementById('edit_id').value = id;
            document.getElementById('edit_grade').value = grade;
            document.getElementById('edit_day').value = day;
            document.getElementById('edit_start_time').value = startTime;
            document.getElementById('edit_end_time').value = endTime;

            // Set subject dropdown if it exists
            const subjectDropdown = document.getElementById('edit_subject');
            if (subjectDropdown) {
                subjectDropdown.value = subjectId;
            }

            editModal.show();
        });
    });

    // Validate edit form
    const editForm = document.querySelector('#editModal form');

    editForm.addEventListener('submit', function(event) {
        const startTime = document.getElementById('edit_start_time').value;
        const endTime = document.getElementById('edit_end_time').value;

        if (startTime && endTime) {
            if (startTime >= endTime) {
                event.preventDefault();
                alert('End time must be later than start time.');
            }
        }
    });

    // Real-time validation for edit form
    const editStartTimeInput = document.getElementById('edit_start_time');
    const editEndTimeInput = document.getElementById('edit_end_time');

    function validateEditTimes() {
        if (editStartTimeInput.value && editEndTimeInput.value) {
            if (editStartTimeInput.value >= editEndTimeInput.value) {
                editEndTimeInput.setCustomValidity('End time must be later than start time');
            } else {
                editEndTimeInput.setCustomValidity('');
            }
        }
    }

    editStartTimeInput.addEventListener('change', validateEditTimes);
    editEndTimeInput.addEventListener('change', validateEditTimes);

    // Notification preferences are now managed in the settings page
});
</script>
