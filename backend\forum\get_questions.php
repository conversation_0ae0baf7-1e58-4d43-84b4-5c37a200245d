<?php
require_once '../server/db_connect.php';
session_start();

// Set header to JSON
header('Content-Type: application/json');

// Debug: Log session data
error_log("Session data in get_questions.php: " . print_r($_SESSION, true));

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['error' => 'Not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'] ?? '';

// Debug: Log user info
error_log("User ID: $user_id, Role: $user_role");

try {
    if ($user_role === 'teacher') {
        // First, check if the teacher exists and get their teacher_id
        $teacherCheck = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
        $teacherCheck->bind_param("i", $user_id);
        $teacherCheck->execute();
        $teacherResult = $teacherCheck->get_result();

        if ($teacherResult->num_rows === 0) {
            echo json_encode(['error' => 'Teacher record not found']);
            exit;
        }

        $teacherData = $teacherResult->fetch_assoc();
        $teacher_id = $teacherData['teacher_id'];

        // Teachers see all questions for subjects they teach with student names
        $stmt = $conn->prepare("
            SELECT q.*, s.subject_name, u.first_name, u.last_name
            FROM questions q
            JOIN subjects s ON q.subject_id = s.subject_id
            JOIN students st ON q.student_id = st.student_id
            JOIN users u ON st.user_id = u.id
            WHERE q.subject_id IN (
                SELECT ts.subject_id
                FROM teacher_subjects ts
                WHERE ts.teacher_id = ?
            )
            ORDER BY q.created_at DESC
        ");
        $stmt->bind_param("i", $teacher_id);
    } else if ($user_role === 'student') {
        // First, check if the student exists and get their student_id
        $studentCheck = $conn->prepare("SELECT student_id FROM students WHERE user_id = ?");
        $studentCheck->bind_param("i", $user_id);
        $studentCheck->execute();
        $studentResult = $studentCheck->get_result();

        if ($studentResult->num_rows === 0) {
            echo json_encode(['error' => 'Student record not found']);
            exit;
        }

        $studentData = $studentResult->fetch_assoc();
        $student_id = $studentData['student_id'];

        // Students see only their questions
        $stmt = $conn->prepare("
            SELECT q.*, s.subject_name,
                   IFNULL(q.is_new_reply, 0) as is_new_reply,
                   IFNULL(q.is_notified, 0) as is_notified
            FROM questions q
            JOIN subjects s ON q.subject_id = s.subject_id
            WHERE q.student_id = ?
            ORDER BY
                CASE WHEN q.is_new_reply = 1 THEN 0 ELSE 1 END,
                q.replied_at DESC,
                q.created_at DESC
        ");
        $stmt->bind_param("i", $student_id);
    } else {
        echo json_encode(['error' => 'Invalid user role']);
        exit;
    }

    // Execute the query
    if (!$stmt->execute()) {
        throw new Exception("Query execution failed: " . $stmt->error);
    }

    $result = $stmt->get_result();
    $questions = $result->fetch_all(MYSQLI_ASSOC);

    // Debug: Log the number of questions found
    $questionCount = count($questions);
    error_log("Found $questionCount questions for user $user_id with role $user_role");

    if ($questionCount === 0 && $user_role === 'teacher') {
        // Get the subject names for the teacher
        $subjectStmt = $conn->prepare("
            SELECT GROUP_CONCAT(s.subject_name SEPARATOR ', ') as subject_names
            FROM teacher_subjects ts
            JOIN subjects s ON ts.subject_id = s.subject_id
            WHERE ts.teacher_id = ?
        ");
        $subjectStmt->bind_param("i", $teacher_id);
        $subjectStmt->execute();
        $subjectResult = $subjectStmt->get_result();

        if ($subjectResult->num_rows > 0) {
            $subjectData = $subjectResult->fetch_assoc();
            $subjectNames = $subjectData['subject_names'];
            echo json_encode(['error' => "No questions found for your subjects: $subjectNames"]);
        } else {
            echo json_encode(['error' => "No questions found for your subjects"]);
        }
        $subjectStmt->close();
    } else {
        echo json_encode($questions);
    }
} catch (Exception $e) {
    error_log("Error in get_questions.php: " . $e->getMessage());
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
} finally {
    if (isset($stmt)) $stmt->close();
    if (isset($conn)) $conn->close();
}
?>
