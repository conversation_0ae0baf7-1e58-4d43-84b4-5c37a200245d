<?php
session_start();

// Check if user is logged in and is a worker
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'worker') {
    header("Location: ../../index.php");
    exit();
}

// Set current page for sidebar highlighting
$currentPage = basename($_SERVER['PHP_SELF']);
include_once '../../assets/worker_sidebar.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <title>Worker Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <style>
    body { margin: 0; padding: 0; }
    .content { margin-left: 260px; padding: 30px; }
    .dashboard-card {
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      padding: 25px;
      margin-bottom: 20px;
      transition: transform 0.3s;
      text-align: center;
      height: 100%;
    }
    .dashboard-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }
    .dashboard-card i {
      font-size: 48px;
      margin-bottom: 15px;
      color: #4e73df;
    }
    .card-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
    }
    @media (max-width: 768px) {
      .content { margin-left: 0; padding: 15px; }
    }
  </style>
</head>
<body>

<div class="content">
  <h2 class="mb-4">Worker Dashboard</h2>
  <p class="text-muted mb-4">Welcome, <?= htmlspecialchars($_SESSION['user_name'] ?? 'Worker') ?>! Manage student attendance and view reports.</p>

  <div class="card-container">
    <a href="qr_scanner.php" class="text-decoration-none">
      <div class="dashboard-card">
        <i class="fas fa-qrcode"></i>
        <h4>QR Scanner</h4>
        <p class="text-muted">Scan student QR codes to mark attendance</p>
      </div>
    </a>

    <a href="monthly_attendance.php" class="text-decoration-none">
      <div class="dashboard-card">
        <i class="fas fa-calendar-check"></i>
        <h4>Monthly Attendance</h4>
        <p class="text-muted">View and manage monthly attendance records</p>
      </div>
    </a>

    <a href="#" class="text-decoration-none">
      <div class="dashboard-card">
        <i class="fas fa-chart-bar"></i>
        <h4>Attendance Reports</h4>
        <p class="text-muted">Generate and export attendance reports</p>
      </div>
    </a>

    <a href="#" class="text-decoration-none">
      <div class="dashboard-card">
        <i class="fas fa-user-graduate"></i>
        <h4>Student Directory</h4>
        <p class="text-muted">Browse and search student information</p>
      </div>
    </a>
  </div>

  <div class="row mt-4">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Recent Attendance</h5>
        </div>
        <div class="card-body">
          <div id="recentAttendance" class="list-group">
            <!-- Will be populated via AJAX -->
            <div class="text-center py-3">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="card">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0">Attendance Statistics</h5>
        </div>
        <div class="card-body">
          <canvas id="attendanceChart"></canvas>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
  // Load recent attendance
  $.ajax({
    url: '../../backend/attendance/get_recent_attendance.php',
    type: 'GET',
    success: function(data) {
      let html = '';
      if (data.length > 0) {
        data.forEach(item => {
          html += `
            <div class="list-group-item">
              <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">${item.student_name}</h6>
                <small>${new Date(item.date + ' ' + item.time).toLocaleString()}</small>
              </div>
              <p class="mb-1">Grade: ${item.grade} | Subject: ${item.subject_name}</p>
              <small>Week: ${item.week}</small>
            </div>
          `;
        });
      } else {
        html = '<div class="text-center py-3">No recent attendance records</div>';
      }
      $('#recentAttendance').html(html);
    },
    error: function() {
      $('#recentAttendance').html('<div class="text-center py-3">Failed to load attendance data</div>');
    }
  });

  // Initialize attendance chart
  const ctx = document.getElementById('attendanceChart').getContext('2d');
  const chart = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
      datasets: [{
        label: 'Attendance Count',
        data: [0, 0, 0, 0],
        backgroundColor: [
          'rgba(75, 192, 192, 0.2)',
          'rgba(54, 162, 235, 0.2)',
          'rgba(153, 102, 255, 0.2)',
          'rgba(255, 159, 64, 0.2)'
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(153, 102, 255, 1)',
          'rgba(255, 159, 64, 1)'
        ],
        borderWidth: 1
      }]
    },
    options: {
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            precision: 0
          }
        }
      }
    }
  });

  // Load chart data
  $.ajax({
    url: '../../backend/attendance/get_attendance_stats.php',
    type: 'GET',
    success: function(data) {
      chart.data.datasets[0].data = [
        data.week1 || 0,
        data.week2 || 0,
        data.week3 || 0,
        data.week4 || 0
      ];
      chart.update();
    }
  });
});
</script>
</body>
</html>