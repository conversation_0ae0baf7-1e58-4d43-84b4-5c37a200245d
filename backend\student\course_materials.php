<?php
session_start();
include '../../backend/server/db_connect.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$subject_id = $_GET['subject_id'];
$grade = $_GET['grade'];

// Fetch subject name
$sub = $conn->prepare("SELECT subject_name FROM subjects WHERE subject_id = ?");
$sub->bind_param("i", $subject_id);
$sub->execute();
$sub->bind_result($subject_name);
$sub->fetch();
$sub->close();

// Fetch materials
$stmt = $conn->prepare("SELECT * FROM materials WHERE subject_id = ? AND grade = ? ORDER BY uploaded_at DESC");
$stmt->bind_param("is", $subject_id, $grade);
$stmt->execute();
$materials = $stmt->get_result();
?>
