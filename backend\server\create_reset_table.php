<?php
// Include database connection
include_once 'db_connect.php';

// SQL to create the password_reset_tokens table
$sql = "
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(64) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    used TINYINT(1) DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY (token)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

// Execute the query
if ($conn->query($sql) === TRUE) {
    echo "Table 'password_reset_tokens' created successfully!";
    echo "<p><a href='../../frontend/login.php'>Return to login page</a></p>";
} else {
    echo "Error creating table: " . $conn->error;
}

// Close the connection
$conn->close();
?>
