<?php
// This is a test script to verify our authentication system works
echo "<h1>Authentication Test</h1>";

// Try to access a protected page
$protected_page = "frontend/dashboards/student_dashboard.php";
echo "<p>Trying to access: $protected_page</p>";

// Include the authentication file
require_once 'backend/server/auth.php';

// Check if the user has access to the page
$has_access = hasPageAccess($protected_page);

if ($has_access) {
    echo "<p style='color: green;'>Access granted!</p>";
} else {
    echo "<p style='color: red;'>Access denied! You would be redirected to the login page.</p>";
    echo "<p>Redirect URL: " . getLoginRedirectPath() . "</p>";
}

// Show current session information
echo "<h2>Current Session Information</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Show a link to test the login page
echo "<p><a href='frontend/login.php'>Go to Login Page</a></p>";
?>
