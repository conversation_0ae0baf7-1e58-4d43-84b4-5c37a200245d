<?php
session_start();
include '../../backend/server/db_connect.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get student details
$stmt = $conn->prepare("
    SELECT u.first_name, u.last_name, u.email, s.grade, s.profile_image
    FROM users u
    JOIN students s ON u.id = s.user_id
    WHERE u.id = ?
");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$student = $stmt->get_result()->fetch_assoc();
$stmt->close();

// Combine first_name and last_name for display
$student['user_name'] = $student['first_name'] . ' ' . $student['last_name'];

// Image fallback
$imagePath = $student['profile_image'] ? $student['profile_image'] : '../../assets/de.jpeg';
?>
