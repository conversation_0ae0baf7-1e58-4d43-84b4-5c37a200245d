<?php
// Include database connection
include_once 'backend/server/db_connect.php';

// Check if submissions table exists
$result = $conn->query("SHOW TABLES LIKE 'submissions'");
if ($result->num_rows == 0) {
    echo "Submissions table does not exist!";
    exit;
}

// Get table structure
$result = $conn->query("DESCRIBE submissions");
echo "<h2>Submissions Table Structure</h2>";
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "<td>" . $row['Extra'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check for sample data
$result = $conn->query("SELECT * FROM submissions LIMIT 5");
echo "<h2>Sample Data</h2>";
if ($result->num_rows > 0) {
    echo "<table border='1'>";
    $first = true;
    while ($row = $result->fetch_assoc()) {
        if ($first) {
            echo "<tr>";
            foreach ($row as $key => $value) {
                echo "<th>" . $key . "</th>";
            }
            echo "</tr>";
            $first = false;
        }
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . $value . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No data found in submissions table.";
}

// Check for marks column
$result = $conn->query("SHOW COLUMNS FROM submissions LIKE 'marks'");
if ($result->num_rows == 0) {
    echo "<h2>Error: 'marks' column does not exist in submissions table!</h2>";
    
    // Create the marks column if it doesn't exist
    echo "<h3>Adding 'marks' column to submissions table...</h3>";
    if ($conn->query("ALTER TABLE submissions ADD COLUMN marks VARCHAR(10) NULL AFTER file_path")) {
        echo "Successfully added 'marks' column!";
    } else {
        echo "Error adding 'marks' column: " . $conn->error;
    }
}

// Check for comments column
$result = $conn->query("SHOW COLUMNS FROM submissions LIKE 'comments'");
if ($result->num_rows == 0) {
    echo "<h2>Error: 'comments' column does not exist in submissions table!</h2>";
    
    // Create the comments column if it doesn't exist
    echo "<h3>Adding 'comments' column to submissions table...</h3>";
    if ($conn->query("ALTER TABLE submissions ADD COLUMN comments TEXT NULL AFTER marks")) {
        echo "Successfully added 'comments' column!";
    } else {
        echo "Error adding 'comments' column: " . $conn->error;
    }
}

// Check for submitted_at column
$result = $conn->query("SHOW COLUMNS FROM submissions LIKE 'submitted_at'");
if ($result->num_rows == 0) {
    echo "<h2>Error: 'submitted_at' column does not exist in submissions table!</h2>";
    
    // Create the submitted_at column if it doesn't exist
    echo "<h3>Adding 'submitted_at' column to submissions table...</h3>";
    if ($conn->query("ALTER TABLE submissions ADD COLUMN submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER comments")) {
        echo "Successfully added 'submitted_at' column!";
    } else {
        echo "Error adding 'submitted_at' column: " . $conn->error;
    }
}

$conn->close();
?>
